// Simple standalone test to verify Qt setup works
// Compile with: g++ -I"D:\Qt\6.9.0\msvc2022_64\include" test_qt_standalone.cpp -L"D:\Qt\6.9.0\msvc2022_64\lib" -lQt6Core -lQt6Gui -lQt6Widgets

#include <QApplication>
#include <QMainWindow>
#include <QLabel>
#include <QVBoxLayout>
#include <QWidget>
#include <QDebug>
#include <QDir>
#include <QCoreApplication>
#include <iostream>

int main(int argc, char *argv[])
{
    try {
        std::cout << "Testing Qt setup..." << std::endl;
        
        // Set plugin path before creating QApplication
        QString currentDir = QDir::currentPath();
        std::cout << "Current directory: " << currentDir.toStdString() << std::endl;
        
        // Try to find Qt plugins in build directory
        QString pluginPath = currentDir + "/build/Debug";
        QCoreApplication::addLibraryPath(pluginPath);
        qputenv("QT_PLUGIN_PATH", pluginPath.toLocal8Bit());
        
        std::cout << "Set plugin path to: " << pluginPath.toStdString() << std::endl;
        
        // Create QApplication
        QApplication app(argc, argv);
        std::cout << "QApplication created successfully!" << std::endl;
        
        // Create a simple window
        QMainWindow window;
        window.setWindowTitle("Qt Test Window");
        window.resize(400, 300);
        
        QWidget* centralWidget = new QWidget(&window);
        window.setCentralWidget(centralWidget);
        
        QVBoxLayout* layout = new QVBoxLayout(centralWidget);
        
        QLabel* label = new QLabel("Qt is working correctly!", centralWidget);
        layout->addWidget(label);
        
        window.show();
        std::cout << "Window shown successfully!" << std::endl;
        
        // Run for a few seconds then exit
        QTimer::singleShot(3000, &app, &QApplication::quit);
        
        return app.exec();
    }
    catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
    catch (...) {
        std::cerr << "Unknown error occurred" << std::endl;
        return 1;
    }
}
