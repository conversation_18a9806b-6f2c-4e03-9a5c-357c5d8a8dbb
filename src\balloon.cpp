#include "balloon.hpp"

#include "common_header.hpp"

#include <NXOpen/Annotations_IdSymbol.hxx>
#include <NXOpen/Annotations_IdSymbolBuilder.hxx>
#include <NXOpen/Annotations_IdSymbolCollection.hxx>
#include <NXOpen/ColorManager.hxx>
#include <NXOpen/Drawings_DraftingViewCollection.hxx>
#include <NXOpen/Drawings_ProjectedView.hxx>
#include <NXOpen/SelectDisplayableObject.hxx>
#include <NXOpen/Annotations_LeaderDataList.hxx>
#include <NXOpen/Annotations_BalloonNote.hxx>
#include <NXOpen/Annotations_BalloonNoteBuilder.hxx>
#include <NXOpen/Annotations_PmiAttributeCollection.hxx>

void CreateBalloonPmiNote(NXOpen::Point3d point1) {
    //NXOpen::Annotations::BalloonNote* nullNXOpen_Annotations_BalloonNote(NULL);
    auto  workPart            = NXOpen::Session::GetSession()->Parts()->Work();
    auto* balloonNoteBuilder1 = workPart->PmiManager()->PmiAttributes()->CreateBalloonNoteBuilder(nullptr);
    balloonNoteBuilder1->Origin()->SetInferRelativeToGeometry(true);
    balloonNoteBuilder1->Origin()->SetAnchor(NXOpen::Annotations::OriginBuilder::AlignmentPositionMidCenter);
    balloonNoteBuilder1->Origin()->Plane()->SetPlaneMethod(NXOpen::Annotations::PlaneBuilder::PlaneMethodTypeModelView);
    balloonNoteBuilder1->SetTitle("BCT_QID_222");
    balloonNoteBuilder1->SetCategory("User Defined");
    balloonNoteBuilder1->SetIdentifier("User Defined");
    balloonNoteBuilder1->SetRevision("-");
    balloonNoteBuilder1->SetBalloonText("222");
    std::vector<NXOpen::NXString> text1(1);
    text1[0] = "Text: BCT";
    balloonNoteBuilder1->SetText(text1);

    //theSession->SetUndoMarkName(markId1, "Balloon Dialog");

    balloonNoteBuilder1->Origin()->Plane()->SetPlaneMethod(NXOpen::Annotations::PlaneBuilder::PlaneMethodTypeModelView);

    balloonNoteBuilder1->Origin()->SetInferRelativeToGeometry(true);

    NXOpen::Annotations::LeaderData* leaderData1;
    leaderData1 = workPart->Annotations()->CreateLeaderData();

    leaderData1->SetStubSize(5.0);

    leaderData1->SetArrowhead(NXOpen::Annotations::LeaderData::ArrowheadTypeFilledArrow);

    leaderData1->SetVerticalAttachment(NXOpen::Annotations::LeaderVerticalAttachmentCenter);

    balloonNoteBuilder1->Leader()->Leaders()->Append(leaderData1);

    leaderData1->SetStubSide(NXOpen::Annotations::LeaderSideInferred);

    std::vector<NXOpen::NXString> text2(1);
    text2[0] = "Text: BCT";
    balloonNoteBuilder1->SetText(text2);

    balloonNoteBuilder1->Origin()->SetInferRelativeToGeometry(true);

    balloonNoteBuilder1->Origin()->SetInferRelativeToGeometry(true);

    NXOpen::Annotations::Annotation::AssociativeOriginData assocOrigin1;
    assocOrigin1.OriginType = NXOpen::Annotations::AssociativeOriginTypeDrag;
    NXOpen::View* nullNXOpen_View(NULL);
    assocOrigin1.View           = nullNXOpen_View;
    assocOrigin1.ViewOfGeometry = nullNXOpen_View;
    NXOpen::Point* nullNXOpen_Point(NULL);
    assocOrigin1.PointOnGeometry = nullNXOpen_Point;
    NXOpen::Annotations::Annotation* nullNXOpen_Annotations_Annotation(NULL);
    assocOrigin1.VertAnnotation          = nullNXOpen_Annotations_Annotation;
    assocOrigin1.VertAlignmentPosition   = NXOpen::Annotations::AlignmentPositionTopLeft;
    assocOrigin1.HorizAnnotation         = nullNXOpen_Annotations_Annotation;
    assocOrigin1.HorizAlignmentPosition  = NXOpen::Annotations::AlignmentPositionTopLeft;
    assocOrigin1.AlignedAnnotation       = nullNXOpen_Annotations_Annotation;
    assocOrigin1.DimensionLine           = 0;
    assocOrigin1.AssociatedView          = nullNXOpen_View;
    assocOrigin1.AssociatedPoint         = nullNXOpen_Point;
    assocOrigin1.OffsetAnnotation        = nullNXOpen_Annotations_Annotation;
    assocOrigin1.OffsetAlignmentPosition = NXOpen::Annotations::AlignmentPositionTopLeft;
    assocOrigin1.XOffsetFactor           = 0.0;
    assocOrigin1.YOffsetFactor           = 0.0;
    assocOrigin1.StackAlignmentPosition  = NXOpen::Annotations::StackAlignmentPositionAbove;
    balloonNoteBuilder1->Origin()->SetAssociativeOrigin(assocOrigin1);

    //NXOpen::Point3d point1(136.17650229859706, 233.07563085887773, 102.46398737356002);
    balloonNoteBuilder1->Origin()->Origin()->SetValue(NULL, nullNXOpen_View, point1);

    balloonNoteBuilder1->Origin()->SetInferRelativeToGeometry(true);

    NXOpen::NXObject* nXObject1;
    nXObject1 = balloonNoteBuilder1->Commit();
}
