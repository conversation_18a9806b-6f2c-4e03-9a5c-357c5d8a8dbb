#include "balloon.hpp"
#include "common_header.hpp"

// NX Open includes
#include <NXOpen/Annotations_IdSymbol.hxx>
#include <NXOpen/Annotations_IdSymbolBuilder.hxx>
#include <NXOpen/Annotations_IdSymbolCollection.hxx>
#include <NXOpen/ColorManager.hxx>
#include <NXOpen/Drawings_DraftingViewCollection.hxx>
#include <NXOpen/Drawings_ProjectedView.hxx>
#include <NXOpen/SelectDisplayableObject.hxx>
#include <NXOpen/Annotations_LeaderDataList.hxx>
#include <NXOpen/Annotations_BalloonNote.hxx>
#include <NXOpen/Annotations_BalloonNoteBuilder.hxx>
#include <NXOpen/Annotations_PmiAttributeCollection.hxx>
#include <NXOpen/Annotations_Dimension.hxx>
#include <NXOpen/Annotations_DraftingFcf.hxx>
#include <NXOpen/Annotations_LeaderData.hxx>
#include <NXOpen/Annotations_OriginBuilder.hxx>
#include <NXOpen/Annotations_PlaneBuilder.hxx>

// Standard includes
#include <stdexcept>
#include <sstream>
#include <algorithm>

//=============================================================================
// PmiBalloon Implementation
//=============================================================================

PmiBalloon::PmiBalloon() {
    // Initialize with default properties
    m_properties.id = 100;
    m_properties.text = "100";
    m_properties.title = "PMI Balloon";
    m_properties.size = 15.0;
    m_properties.textColor = "Red";
    m_properties.borderColor = "Blue";
}

PmiBalloon::PmiBalloon(const BalloonProperties& props) : m_properties(props) {
}

PmiBalloon::~PmiBalloon() {
    // Note: We don't destroy the NX object here as it's managed by NX
    // The destroy() method should be called explicitly if needed
}

NXOpen::NXObject* PmiBalloon::create() {
    if (m_isCreated && m_nxObject) {
        return m_nxObject; // Already created
    }

    try {
        auto workPart = NXOpen::Session::GetSession()->Parts()->Work();
        if (!workPart) {
            throw std::runtime_error("No work part available");
        }

        // Create balloon note builder
        NXOpen::Annotations::BalloonNote* balloonNote = nullptr;
        auto balloonNoteBuilder = workPart->PmiManager()->PmiAttributes()->CreateBalloonNoteBuilder(balloonNote);

        if (!balloonNoteBuilder) {
            throw std::runtime_error("Failed to create balloon note builder");
        }

        // Setup the builder with our properties
        setupBuilder(balloonNoteBuilder);

        // Configure leader if needed
        if (m_properties.hasLeader) {
            configureLeader(balloonNoteBuilder);
        }

        // Apply colors
        applyColors(balloonNoteBuilder);

        // Set position
        NXOpen::View* view = nullptr;
        balloonNoteBuilder->Origin()->Origin()->SetValue(nullptr, view, m_properties.position.toNXPoint());

        // Commit the balloon
        m_nxObject = balloonNoteBuilder->Commit();
        balloonNoteBuilder->Destroy();

        m_isCreated = true;
        return m_nxObject;
    }
    catch (const std::exception& e) {
        throw std::runtime_error(std::string("Failed to create PMI balloon: ") + e.what());
    }
}

bool PmiBalloon::update() {
    if (!m_isCreated || !m_nxObject) {
        return false;
    }

    try {
        // For updates, we need to recreate the balloon with new properties
        // NX doesn't support direct property updates on committed objects
        destroy();
        create();
        return true;
    }
    catch (const std::exception&) {
        return false;
    }
}

bool PmiBalloon::destroy() {
    if (m_nxObject) {
        try {
            // Note: In NX, objects are typically managed by the session
            // We just reset our reference
            m_nxObject = nullptr;
            m_isCreated = false;
            return true;
        }
        catch (const std::exception&) {
            return false;
        }
    }
    return true;
}

void PmiBalloon::setProperties(const BalloonProperties& props) {
    m_properties = props;
    // If already created, mark for update
    if (m_isCreated) {
        update();
    }
}

BalloonProperties PmiBalloon::getProperties() const {
    return m_properties;
}

void PmiBalloon::setPosition(const Point3D& position) {
    m_properties.position = position;
    if (m_isCreated) {
        update();
    }
}

Point3D PmiBalloon::getPosition() const {
    return m_properties.position;
}

void PmiBalloon::setText(const std::string& text) {
    m_properties.text = text;
    if (m_isCreated) {
        update();
    }
}

std::string PmiBalloon::getText() const {
    return m_properties.text;
}

bool PmiBalloon::isValid() const {
    return !m_properties.text.empty() &&
           m_properties.size > 0 &&
           m_properties.id >= 0;
}

void PmiBalloon::attachToDimension(NXOpen::Annotations::Dimension* dimension) {
    if (dimension) {
        // Calculate position relative to dimension
        auto annotationOrigin = dimension->AnnotationOrigin();
        m_properties.position = Point3D(annotationOrigin.X - 3, annotationOrigin.Y - 3, annotationOrigin.Z);

        // Get text angle from dimension if it's a linear dimension
        // This would require more specific dimension type checking
        m_properties.textAngle = 0.0; // Default for now

        if (m_isCreated) {
            update();
        }
    }
}

void PmiBalloon::attachToFcf(NXOpen::Annotations::DraftingFcf* fcf) {
    if (fcf) {
        // Calculate position relative to FCF
        auto annotationOrigin = fcf->AnnotationOrigin();
        m_properties.position = Point3D(annotationOrigin.X - 3, annotationOrigin.Y - 3, annotationOrigin.Z);

        if (m_isCreated) {
            update();
        }
    }
}

// Helper methods implementation
void PmiBalloon::setupBuilder(void* builder) {
    auto balloonNoteBuilder = static_cast<NXOpen::Annotations::BalloonNoteBuilder*>(builder);

    // Set basic properties
    balloonNoteBuilder->Origin()->SetInferRelativeToGeometry(true);
    balloonNoteBuilder->Origin()->SetAnchor(NXOpen::Annotations::OriginBuilder::AlignmentPositionMidCenter);
    balloonNoteBuilder->Origin()->Plane()->SetPlaneMethod(NXOpen::Annotations::PlaneBuilder::PlaneMethodTypeModelView);

    // Set text properties
    balloonNoteBuilder->SetTitle(m_properties.title.c_str());
    balloonNoteBuilder->SetCategory(m_properties.category.c_str());
    balloonNoteBuilder->SetIdentifier(m_properties.identifier.c_str());
    balloonNoteBuilder->SetRevision(m_properties.revision.c_str());
    balloonNoteBuilder->SetBalloonText(m_properties.text.c_str());

    // Set additional text if any
    if (!m_properties.additionalText.empty()) {
        std::vector<NXOpen::NXString> textVector;
        for (const auto& text : m_properties.additionalText) {
            textVector.push_back(text.c_str());
        }
        balloonNoteBuilder->SetText(textVector);
    }
}

void PmiBalloon::configureLeader(void* builder) {
    auto balloonNoteBuilder = static_cast<NXOpen::Annotations::BalloonNoteBuilder*>(builder);
    auto workPart = NXOpen::Session::GetSession()->Parts()->Work();

    // Create leader data
    auto leaderData = workPart->Annotations()->CreateLeaderData();

    // Configure leader properties
    leaderData->SetStubSize(m_properties.stubSize);
    leaderData->SetArrowhead(convertArrowType(m_properties.arrowType));
    leaderData->SetVerticalAttachment(NXOpen::Annotations::LeaderVerticalAttachmentCenter);
    leaderData->SetStubSide(NXOpen::Annotations::LeaderSideInferred);

    // Add leader to balloon
    balloonNoteBuilder->Leader()->Leaders()->Append(leaderData);
}

void PmiBalloon::applyColors(void* builder) {
    auto balloonNoteBuilder = static_cast<NXOpen::Annotations::BalloonNoteBuilder*>(builder);
    auto workPart = NXOpen::Session::GetSession()->Parts()->Work();

    try {
        // Set text color
        auto textColor = workPart->Colors()->Find(m_properties.textColor.c_str());
        if (textColor) {
            // Note: The exact method to set colors may vary depending on NX version
            // This is a simplified approach
        }

        // Set border color
        auto borderColor = workPart->Colors()->Find(m_properties.borderColor.c_str());
        if (borderColor) {
            // Note: The exact method to set colors may vary depending on NX version
        }
    }
    catch (const std::exception&) {
        // Color setting failed, continue with defaults
    }
}

NXOpen::Annotations::LeaderData::ArrowheadType PmiBalloon::convertArrowType(ArrowType type) const {
    switch (type) {
        case ArrowType::FILLED_ARROW:
            return NXOpen::Annotations::LeaderData::ArrowheadTypeFilledArrow;
        case ArrowType::OPEN_ARROW:
            return NXOpen::Annotations::LeaderData::ArrowheadTypeOpenArrow;
        case ArrowType::CLOSED_ARROW:
            return NXOpen::Annotations::LeaderData::ArrowheadTypeClosedArrow;
        case ArrowType::DOT:
            return NXOpen::Annotations::LeaderData::ArrowheadTypeDot;
        case ArrowType::NO_ARROW:
            return NXOpen::Annotations::LeaderData::ArrowheadTypeNone;
        default:
            return NXOpen::Annotations::LeaderData::ArrowheadTypeFilledArrow;
    }
}

//=============================================================================
// BalloonBuilder Implementation
//=============================================================================

BalloonBuilder::BalloonBuilder() {
    reset();
}

BalloonBuilder& BalloonBuilder::setId(int id) {
    m_properties.id = id;
    return *this;
}

BalloonBuilder& BalloonBuilder::setText(const std::string& text) {
    m_properties.text = text;
    return *this;
}

BalloonBuilder& BalloonBuilder::setTitle(const std::string& title) {
    m_properties.title = title;
    return *this;
}

BalloonBuilder& BalloonBuilder::setCategory(const std::string& category) {
    m_properties.category = category;
    return *this;
}

BalloonBuilder& BalloonBuilder::setIdentifier(const std::string& identifier) {
    m_properties.identifier = identifier;
    return *this;
}

BalloonBuilder& BalloonBuilder::setRevision(const std::string& revision) {
    m_properties.revision = revision;
    return *this;
}

BalloonBuilder& BalloonBuilder::setPosition(const Point3D& position) {
    m_properties.position = position;
    return *this;
}

BalloonBuilder& BalloonBuilder::setPosition(double x, double y, double z) {
    m_properties.position = Point3D(x, y, z);
    return *this;
}

BalloonBuilder& BalloonBuilder::setSize(double size) {
    m_properties.size = size;
    return *this;
}

BalloonBuilder& BalloonBuilder::setTextAngle(double angle) {
    m_properties.textAngle = angle;
    return *this;
}

BalloonBuilder& BalloonBuilder::setShape(BalloonShape shape) {
    m_properties.shape = shape;
    return *this;
}

BalloonBuilder& BalloonBuilder::setArrowType(ArrowType arrowType) {
    m_properties.arrowType = arrowType;
    return *this;
}

BalloonBuilder& BalloonBuilder::setAlignment(TextAlignment alignment) {
    m_properties.alignment = alignment;
    return *this;
}

BalloonBuilder& BalloonBuilder::setTextColor(const std::string& color) {
    m_properties.textColor = color;
    return *this;
}

BalloonBuilder& BalloonBuilder::setBackgroundColor(const std::string& color) {
    m_properties.backgroundColor = color;
    return *this;
}

BalloonBuilder& BalloonBuilder::setBorderColor(const std::string& color) {
    m_properties.borderColor = color;
    return *this;
}

BalloonBuilder& BalloonBuilder::setStubSize(double size) {
    m_properties.stubSize = size;
    return *this;
}

BalloonBuilder& BalloonBuilder::setHasLeader(bool hasLeader) {
    m_properties.hasLeader = hasLeader;
    return *this;
}

BalloonBuilder& BalloonBuilder::addAdditionalText(const std::string& text) {
    m_properties.additionalText.push_back(text);
    return *this;
}

// Preset configurations
BalloonBuilder& BalloonBuilder::useDefaultPmiSettings() {
    m_properties.category = "User Defined";
    m_properties.identifier = "User Defined";
    m_properties.revision = "-";
    m_properties.size = 15.0;
    m_properties.textColor = "Red";
    m_properties.borderColor = "Blue";
    m_properties.backgroundColor = "White";
    m_properties.hasLeader = true;
    m_properties.arrowType = ArrowType::FILLED_ARROW;
    m_properties.stubSize = 5.0;
    return *this;
}

BalloonBuilder& BalloonBuilder::useIdSymbolSettings() {
    m_properties.category = "ID Symbol";
    m_properties.identifier = "ID Symbol";
    m_properties.size = 15.0;
    m_properties.textColor = "Red";
    m_properties.borderColor = "Blue";
    m_properties.hasLeader = false;
    m_properties.shape = BalloonShape::CIRCLE;
    return *this;
}

BalloonBuilder& BalloonBuilder::useHighlightSettings() {
    m_properties.textColor = "Yellow";
    m_properties.borderColor = "Red";
    m_properties.backgroundColor = "Black";
    m_properties.size = 18.0;
    m_properties.hasLeader = true;
    m_properties.arrowType = ArrowType::FILLED_ARROW;
    return *this;
}

BalloonProperties BalloonBuilder::build() const {
    return m_properties;
}

BalloonBuilder& BalloonBuilder::reset() {
    m_properties = BalloonProperties(); // Reset to default
    return *this;
}

//=============================================================================
// BalloonFactory Implementation
//=============================================================================

std::unique_ptr<IBalloon> BalloonFactory::createPmiBalloon() {
    return std::make_unique<PmiBalloon>();
}

std::unique_ptr<IBalloon> BalloonFactory::createPmiBalloon(const BalloonProperties& props) {
    return std::make_unique<PmiBalloon>(props);
}

std::unique_ptr<IBalloon> BalloonFactory::createIdSymbol() {
    auto props = getDefaultPropertiesForType(BalloonType::ID_SYMBOL);
    return std::make_unique<PmiBalloon>(props);
}

std::unique_ptr<IBalloon> BalloonFactory::createIdSymbol(const BalloonProperties& props) {
    return std::make_unique<PmiBalloon>(props);
}

std::unique_ptr<IBalloon> BalloonFactory::createBalloon(BalloonType type, const BalloonBuilder& builder) {
    auto props = builder.build();

    switch (type) {
        case BalloonType::PMI_BALLOON:
            return std::make_unique<PmiBalloon>(props);
        case BalloonType::ID_SYMBOL:
            return std::make_unique<PmiBalloon>(props);
        case BalloonType::GENERAL_NOTE:
            // For now, use PmiBalloon for all types
            return std::make_unique<PmiBalloon>(props);
        default:
            throw std::invalid_argument("Unsupported balloon type");
    }
}

std::unique_ptr<IBalloon> BalloonFactory::createForDimension(NXOpen::Annotations::Dimension* dimension, int balloonId) {
    if (!dimension) {
        throw std::invalid_argument("Dimension cannot be null");
    }

    auto position = calculatePositionForDimension(dimension);
    auto props = getDefaultPropertiesForType(BalloonType::PMI_BALLOON);
    props.id = balloonId;
    props.text = std::to_string(balloonId);
    props.position = position;

    auto balloon = std::make_unique<PmiBalloon>(props);
    balloon->attachToDimension(dimension);
    return std::move(balloon);
}

std::unique_ptr<IBalloon> BalloonFactory::createForFcf(NXOpen::Annotations::DraftingFcf* fcf, int balloonId) {
    if (!fcf) {
        throw std::invalid_argument("FCF cannot be null");
    }

    auto position = calculatePositionForFcf(fcf);
    auto props = getDefaultPropertiesForType(BalloonType::PMI_BALLOON);
    props.id = balloonId;
    props.text = std::to_string(balloonId);
    props.position = position;

    auto balloon = std::make_unique<PmiBalloon>(props);
    balloon->attachToFcf(fcf);
    return std::move(balloon);
}

std::unique_ptr<IBalloon> BalloonFactory::createAtPosition(const Point3D& position, const std::string& text) {
    auto props = getDefaultPropertiesForType(BalloonType::PMI_BALLOON);
    props.position = position;
    props.text = text;

    return std::make_unique<PmiBalloon>(props);
}

bool BalloonFactory::isTypeSupported(BalloonType type) {
    switch (type) {
        case BalloonType::PMI_BALLOON:
        case BalloonType::ID_SYMBOL:
        case BalloonType::GENERAL_NOTE:
            return true;
        default:
            return false;
    }
}

std::vector<BalloonType> BalloonFactory::getSupportedTypes() {
    return {
        BalloonType::PMI_BALLOON,
        BalloonType::ID_SYMBOL,
        BalloonType::GENERAL_NOTE
    };
}

BalloonProperties BalloonFactory::getDefaultPropertiesForType(BalloonType type) {
    BalloonProperties props;

    switch (type) {
        case BalloonType::PMI_BALLOON:
            props.category = "User Defined";
            props.identifier = "User Defined";
            props.revision = "-";
            props.size = 15.0;
            props.textColor = "Red";
            props.borderColor = "Blue";
            props.hasLeader = true;
            props.arrowType = ArrowType::FILLED_ARROW;
            props.stubSize = 5.0;
            break;

        case BalloonType::ID_SYMBOL:
            props.category = "ID Symbol";
            props.identifier = "ID Symbol";
            props.size = 15.0;
            props.textColor = "Red";
            props.borderColor = "Blue";
            props.hasLeader = false;
            props.shape = BalloonShape::CIRCLE;
            break;

        case BalloonType::GENERAL_NOTE:
            props.category = "General Note";
            props.identifier = "General Note";
            props.size = 12.0;
            props.textColor = "Black";
            props.borderColor = "Black";
            props.hasLeader = false;
            break;
    }

    return props;
}

Point3D BalloonFactory::calculatePositionForDimension(NXOpen::Annotations::Dimension* dimension) {
    if (!dimension) {
        return Point3D(0, 0, 0);
    }

    auto annotationOrigin = dimension->AnnotationOrigin();
    return Point3D(annotationOrigin.X - 3, annotationOrigin.Y - 3, annotationOrigin.Z);
}

Point3D BalloonFactory::calculatePositionForFcf(NXOpen::Annotations::DraftingFcf* fcf) {
    if (!fcf) {
        return Point3D(0, 0, 0);
    }

    auto annotationOrigin = fcf->AnnotationOrigin();
    return Point3D(annotationOrigin.X - 3, annotationOrigin.Y - 3, annotationOrigin.Z);
}

void CreateBalloonPmiNote(NXOpen::Point3d point1) {
    //NXOpen::Annotations::BalloonNote* nullNXOpen_Annotations_BalloonNote(NULL);
    auto  workPart            = NXOpen::Session::GetSession()->Parts()->Work();
    auto* balloonNoteBuilder1 = workPart->PmiManager()->PmiAttributes()->CreateBalloonNoteBuilder(nullptr);
    balloonNoteBuilder1->Origin()->SetInferRelativeToGeometry(true);
    balloonNoteBuilder1->Origin()->SetAnchor(NXOpen::Annotations::OriginBuilder::AlignmentPositionMidCenter);
    balloonNoteBuilder1->Origin()->Plane()->SetPlaneMethod(NXOpen::Annotations::PlaneBuilder::PlaneMethodTypeModelView);
    balloonNoteBuilder1->SetTitle("BCT_QID_222");
    balloonNoteBuilder1->SetCategory("User Defined");
    balloonNoteBuilder1->SetIdentifier("User Defined");
    balloonNoteBuilder1->SetRevision("-");
    balloonNoteBuilder1->SetBalloonText("222");
    std::vector<NXOpen::NXString> text1(1);
    text1[0] = "Text: BCT";
    balloonNoteBuilder1->SetText(text1);

    //theSession->SetUndoMarkName(markId1, "Balloon Dialog");

    balloonNoteBuilder1->Origin()->Plane()->SetPlaneMethod(NXOpen::Annotations::PlaneBuilder::PlaneMethodTypeModelView);

    balloonNoteBuilder1->Origin()->SetInferRelativeToGeometry(true);

    NXOpen::Annotations::LeaderData* leaderData1;
    leaderData1 = workPart->Annotations()->CreateLeaderData();

    leaderData1->SetStubSize(5.0);

    leaderData1->SetArrowhead(NXOpen::Annotations::LeaderData::ArrowheadTypeFilledArrow);

    leaderData1->SetVerticalAttachment(NXOpen::Annotations::LeaderVerticalAttachmentCenter);

    balloonNoteBuilder1->Leader()->Leaders()->Append(leaderData1);

    leaderData1->SetStubSide(NXOpen::Annotations::LeaderSideInferred);

    std::vector<NXOpen::NXString> text2(1);
    text2[0] = "Text: BCT";
    balloonNoteBuilder1->SetText(text2);

    balloonNoteBuilder1->Origin()->SetInferRelativeToGeometry(true);

    balloonNoteBuilder1->Origin()->SetInferRelativeToGeometry(true);

    NXOpen::Annotations::Annotation::AssociativeOriginData assocOrigin1;
    assocOrigin1.OriginType = NXOpen::Annotations::AssociativeOriginTypeDrag;
    NXOpen::View* nullNXOpen_View(NULL);
    assocOrigin1.View           = nullNXOpen_View;
    assocOrigin1.ViewOfGeometry = nullNXOpen_View;
    NXOpen::Point* nullNXOpen_Point(NULL);
    assocOrigin1.PointOnGeometry = nullNXOpen_Point;
    NXOpen::Annotations::Annotation* nullNXOpen_Annotations_Annotation(NULL);
    assocOrigin1.VertAnnotation          = nullNXOpen_Annotations_Annotation;
    assocOrigin1.VertAlignmentPosition   = NXOpen::Annotations::AlignmentPositionTopLeft;
    assocOrigin1.HorizAnnotation         = nullNXOpen_Annotations_Annotation;
    assocOrigin1.HorizAlignmentPosition  = NXOpen::Annotations::AlignmentPositionTopLeft;
    assocOrigin1.AlignedAnnotation       = nullNXOpen_Annotations_Annotation;
    assocOrigin1.DimensionLine           = 0;
    assocOrigin1.AssociatedView          = nullNXOpen_View;
    assocOrigin1.AssociatedPoint         = nullNXOpen_Point;
    assocOrigin1.OffsetAnnotation        = nullNXOpen_Annotations_Annotation;
    assocOrigin1.OffsetAlignmentPosition = NXOpen::Annotations::AlignmentPositionTopLeft;
    assocOrigin1.XOffsetFactor           = 0.0;
    assocOrigin1.YOffsetFactor           = 0.0;
    assocOrigin1.StackAlignmentPosition  = NXOpen::Annotations::StackAlignmentPositionAbove;
    balloonNoteBuilder1->Origin()->SetAssociativeOrigin(assocOrigin1);

    //NXOpen::Point3d point1(136.17650229859706, 233.07563085887773, 102.46398737356002);
    balloonNoteBuilder1->Origin()->Origin()->SetValue(NULL, nullNXOpen_View, point1);

    balloonNoteBuilder1->Origin()->SetInferRelativeToGeometry(true);

    NXOpen::NXObject* nXObject1;
    nXObject1 = balloonNoteBuilder1->Commit();
}
