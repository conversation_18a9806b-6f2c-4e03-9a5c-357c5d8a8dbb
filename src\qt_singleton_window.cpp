#include "qt_singleton_window.h"
#include <QDateTime>
#include <QScrollBar>
#include <QFont>
#include <QSizePolicy>

// Static member definitions for QtSingletonWindow
std::unique_ptr<QtSingletonWindow> QtSingletonWindow::s_instance = nullptr;
std::mutex QtSingletonWindow::s_mutex;

// Static member definitions for QtApplicationManager
std::mutex QtApplicationManager::s_mutex;

// QtSingletonWindow Implementation
QtSingletonWindow::QtSingletonWindow(QWidget* parent)
    : QMainWindow(parent)
    , m_centralWidget(nullptr)
    , m_layout(nullptr)
    , m_titleLabel(nullptr)
    , m_messageArea(nullptr)
    , m_closeButton(nullptr)
    , m_clearButton(nullptr)
{
    setupUI();
}

QtSingletonWindow::~QtSingletonWindow() = default;

QtSingletonWindow* QtSingletonWindow::getInstance()
{
    std::lock_guard<std::mutex> lock(s_mutex);
    if (!s_instance) {
        // Make sure Qt Application is initialized first
        if (!QtApplicationManager::getInstance().isInitialized()) {
            QtApplicationManager::getInstance().initialize();
        }
        s_instance = std::unique_ptr<QtSingletonWindow>(new QtSingletonWindow());
    }
    return s_instance.get();
}

void QtSingletonWindow::setupUI()
{
    // Set window properties
    setWindowTitle("NX Plugin - Qt Singleton Window");
    setMinimumSize(600, 400);
    resize(800, 600);
    
    // Create central widget
    m_centralWidget = new QWidget(this);
    setCentralWidget(m_centralWidget);
    
    // Create layout
    m_layout = new QVBoxLayout(m_centralWidget);
    
    // Create title label
    m_titleLabel = new QLabel("NX Plugin Status Window", this);
    QFont titleFont = m_titleLabel->font();
    titleFont.setPointSize(14);
    titleFont.setBold(true);
    m_titleLabel->setFont(titleFont);
    m_titleLabel->setAlignment(Qt::AlignCenter);
    m_layout->addWidget(m_titleLabel);
    
    // Create message area
    m_messageArea = new QTextEdit(this);
    m_messageArea->setReadOnly(true);
    m_messageArea->setFont(QFont("Consolas", 10));
    m_layout->addWidget(m_messageArea);
    
    // Create buttons layout
    QHBoxLayout* buttonLayout = new QHBoxLayout();
    
    // Create clear button
    m_clearButton = new QPushButton("Clear Messages", this);
    connect(m_clearButton, &QPushButton::clicked, this, &QtSingletonWindow::onClearButtonClicked);
    buttonLayout->addWidget(m_clearButton);
    
    // Add stretch to push close button to the right
    buttonLayout->addStretch();
    
    // Create close button
    m_closeButton = new QPushButton("Hide Window", this);
    connect(m_closeButton, &QPushButton::clicked, this, &QtSingletonWindow::onCloseButtonClicked);
    buttonLayout->addWidget(m_closeButton);
    
    m_layout->addLayout(buttonLayout);
    
    // Add initial message
    addMessage("Qt Singleton Window initialized successfully!");
}

void QtSingletonWindow::showWindow()
{
    show();
    raise();
    activateWindow();
}

void QtSingletonWindow::hideWindow()
{
    hide();
}

void QtSingletonWindow::addMessage(const QString& message)
{
    if (!m_messageArea) return;
    
    QString timestamp = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
    QString formattedMessage = QString("[%1] %2").arg(timestamp, message);
    
    m_messageArea->append(formattedMessage);
    
    // Auto-scroll to bottom
    QScrollBar* scrollBar = m_messageArea->verticalScrollBar();
    scrollBar->setValue(scrollBar->maximum());
}

bool QtSingletonWindow::isWindowVisible() const
{
    return isVisible();
}

void QtSingletonWindow::onCloseButtonClicked()
{
    hideWindow();
}

void QtSingletonWindow::onClearButtonClicked()
{
    if (m_messageArea) {
        m_messageArea->clear();
        addMessage("Messages cleared.");
    }
}

// QtApplicationManager Implementation
QtApplicationManager& QtApplicationManager::getInstance()
{
    static QtApplicationManager instance;
    return instance;
}

bool QtApplicationManager::initialize()
{
    std::lock_guard<std::mutex> lock(s_mutex);

    if (m_initialized) {
        return true; // Already initialized
    }

    try {
        // Check if QApplication already exists
        if (QApplication::instance() == nullptr) {
            m_app = std::make_unique<QApplication>(m_argc, m_argv);

            // Set application properties
            m_app->setApplicationName("NX Plugin");
            m_app->setApplicationVersion("1.0");
            m_app->setOrganizationName("NX Plugin Developer");

            // Prevent the application from quitting when the last window closes
            m_app->setQuitOnLastWindowClosed(false);
        }

        m_initialized = true;
        return true;
    }
    catch (const std::exception& e) {
        // Handle initialization error
        m_initialized = false;
        return false;
    }
}

void QtApplicationManager::cleanup()
{
    std::lock_guard<std::mutex> lock(s_mutex);

    if (!m_initialized) {
        return;
    }

    // Clean up the singleton window first
    QtSingletonWindow::s_instance.reset();

    // Clean up QApplication
    if (m_app) {
        m_app.reset();
    }

    m_initialized = false;
}

bool QtApplicationManager::isInitialized() const
{
    return m_initialized;
}

QApplication* QtApplicationManager::getApplication() const
{
    return m_app.get();
}
