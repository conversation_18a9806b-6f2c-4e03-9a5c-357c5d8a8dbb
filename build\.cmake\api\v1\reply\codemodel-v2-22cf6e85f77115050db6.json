{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "tw_demo", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-Debug-f086712db55135affadf.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-Debug-c86817cdff5dd482d60a.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 0, "id": "tw_demo::@6890427a1f51a3e7e1df", "jsonFile": "target-tw_demo-Debug-26f87200dda895cb0b7e.json", "name": "tw_demo", "projectIndex": 0}]}, {"directories": [{"build": ".", "jsonFile": "directory-.-Release-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "Release", "projects": [{"directoryIndexes": [0], "name": "tw_demo", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-Release-f086712db55135affadf.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-Release-c86817cdff5dd482d60a.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 0, "id": "tw_demo::@6890427a1f51a3e7e1df", "jsonFile": "target-tw_demo-Release-496e4e465380d0cfd0da.json", "name": "tw_demo", "projectIndex": 0}]}, {"directories": [{"build": ".", "jsonFile": "directory-.-MinSizeRel-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "MinSizeRel", "projects": [{"directoryIndexes": [0], "name": "tw_demo", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-MinSizeRel-f086712db55135affadf.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-MinSizeRel-c86817cdff5dd482d60a.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 0, "id": "tw_demo::@6890427a1f51a3e7e1df", "jsonFile": "target-tw_demo-MinSizeRel-711416c3efe44c99cac0.json", "name": "tw_demo", "projectIndex": 0}]}, {"directories": [{"build": ".", "jsonFile": "directory-.-RelWithDebInfo-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "RelWithDebInfo", "projects": [{"directoryIndexes": [0], "name": "tw_demo", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-f086712db55135affadf.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-RelWithDebInfo-c86817cdff5dd482d60a.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 0, "id": "tw_demo::@6890427a1f51a3e7e1df", "jsonFile": "target-tw_demo-RelWithDebInfo-8f037bfddb4510de7d63.json", "name": "tw_demo", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "D:/dev/2025/tw_demo/build", "source": "D:/dev/2025/tw_demo"}, "version": {"major": 2, "minor": 8}}