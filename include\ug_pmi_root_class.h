//  -*- mode: c++ -*-

/// @file     "ug_pmi_root_class.h"
///
/// @brief    TODO
///
/// <AUTHOR> <<EMAIL>>
/// @date     2010-10-28
/// @revision $Id$
//////////////////////////////////////////////////////////////////////////////

#ifndef __ug_pmi_root_class__h__
#define __ug_pmi_root_class__h__

// System includes
// #include <vector>

// Qt includes
#include <QList>
#include <QMap>
#include <QPair>
#include <QString>
// QT_FORWARD_DECLARE_CLASS(QString)

// BCT includes
#include "unigraphics.h"

// Local includes
#include "ug_cad_interface.h"
class CADDataLine;
class CADDataTable;

// UG Open C includes
#include <uf.h>

// NX Open C++ includes
#include <NXOpen/Annotations_Annotation.hxx>
// #include <NXOpen/NXObjectManager.hxx>
// #include <NXOpen/Part.hxx>
// #include <NXOpen/Annotations_Pmi.hxx>
// #include <NXOpen/PartCollection.hxx>
// #include <NXOpen/Annotations_PmiManager.hxx>
// #include <NXOpen/Session.hxx>
namespace NXOpen {
class ModelingView;
class Part;
class Session;
class View;

namespace Annotations {
class BalloonNote;
class Datum;
class Dimension;
class Fcf;
class GeneralNote;
class Pmi;
class PmiAttributeCollection;
class PmiCustomSymbol;
class PmiLineWeld;
class PmiManager;
class PmiUrlNote;
class PointTarget;
class SimpleDraftingAid;
class SurfaceFinish;
struct Lettering;
struct LineCfw;
}
}


class UGPmiRootClass {
public:
    UGPmiRootClass(const tag_t&, const tag_t& = NULL_TAG);
    ~UGPmiRootClass(void) { _PmiObjectCounter -= (_PmiObjectCounter > 0 ? 1 : 0); }

    /* ------------------------------------------------------ */

    UGPmiRootClass& operator=(const UGPmiRootClass&);

    /* ------------------------------------------------------ */

    bool isPmi(void) const;
    static bool isPmi(const tag_t& = NULL_TAG);

    bool isPmiBalloon(std::string* = 0) const;
    bool isPmiToUse(const bool& = false) const;
    bool isPmiInChildOfWorkPart(void) const;

    tag_t tag(void) const;

    static Qt4UgOpen::TagList componentPmis(const tag_t&, const bool& = false, const bool& = true);

    QString debugInfo(void) const;

    bool setLayer(const int&, const int& = 0) const;

    QString name(void) const;
    bool setName(const std::string&, const bool& = true) const;

    bool references(QList<BctInspector::CADInterface::sREFERENCE>* = 0) const;

    /* ------------------------------------------------------ */

    // Attributes: Key: TITLE - Value: value.first  => title (cad), value.second => value (cad)
    QString attributes(const QString&) const;
    QMap<QString, QPair<QString, QString> > attributes(void) const;

    /* ------------------------------------------------------ */

    int index(void) const;

    static double calcPmiBalloonScale(QString, double);
    // unused 2015-06-23 bool setPmiBalloonScale(double = (-1.0)) const;

    bool preferedOriginData(NXOpen::Annotations::Annotation::AssociativeOriginData*) const;

    bool angle(double*) const;
    bool setAngle(const double&) const;

    bool symbolPreferences(NXOpen::Annotations::LineCfw*) const;
    bool setSymbolPreferences(const NXOpen::Annotations::LineCfw&) const;

    bool textPreferences(NXOpen::Annotations::Lettering*) const;
    bool setTextPreferences(const NXOpen::Annotations::Lettering&) const;

    bool setHighlight(const bool&) const;
    bool setVisible(const bool&) const;
    bool remove(void);

    // @warning: The "QList<NXOpen::View*> views(void) const" method is just a Prototype!
    QList<NXOpen::View*> views(void) const;

    static NXOpen::ModelingView* modelingViewOfName(const QString&);

    QList<NXOpen::Annotations::Annotation*> displayInstances(void) const;

    bool getLocationInfo(CADDataLine*) const;
    bool createDataTable(CADDataTable*, const bool& = false) const;

    /* ------------------------------------------------------ */

    NXOpen::Session* const session(void) const;
    NXOpen::Part* const workPart(void) const;
    NXOpen::Part* const owningPart(void) const;
    NXOpen::Annotations::PmiManager* const pmiManager(void) const;
    NXOpen::Annotations::PmiAttributeCollection* pmiAttributes() const;
    NXOpen::Annotations::Pmi* const pmi(void) const { return _pmi; }

private:
    bool fallbackBaseIntegration(CADDataTable*,
                                 const QString&,
                                 const QString&,
                                 const QString& = "Unknown Type") const;

    bool dataDimension(CADDataTable*,
                       const QString&,
                       const QString&,
                       NXOpen::Annotations::Dimension*,
                       const bool& = false) const;

    bool dataFcf(CADDataTable*,
                 const QString&,
                 const QString&,
                 NXOpen::Annotations::Fcf* fcf,
                 const bool& = false) const;

    bool dataDatum(CADDataTable*,
                   const QString&,
                   const QString&,
                   NXOpen::Annotations::Datum*,
                   const bool& = false) const;

    bool dataCustomSymbol(CADDataTable*,
                          const QString&,
                          const QString&,
                          NXOpen::Annotations::PmiCustomSymbol*) const;

    bool dataLabelNote(CADDataTable*,
                       const QString&,
                       const QString&,
                       NXOpen::Annotations::SimpleDraftingAid*,
                       const bool& = false) const;

    bool dataBalloonNote(CADDataTable*,
                         const QString&,
                         const QString&,
                         NXOpen::Annotations::BalloonNote*,
                         const bool& = false) const;

    bool dataGeneralNote(CADDataTable*,
                         const QString&,
                         const QString&,
                         NXOpen::Annotations::GeneralNote*,
                         const bool& = false) const;

    bool dataPmiUrlNote(CADDataTable*,
                        const QString&,
                        const QString&,
                        NXOpen::Annotations::PmiUrlNote*,
                        const bool& = false) const;

    bool dataSurfaceFinish(CADDataTable*,
                           const QString&,
                           const QString&,
                           NXOpen::Annotations::SurfaceFinish*,
                           const bool& = false) const;

    bool dataPointTarget(CADDataTable*,
                         const QString&,
                         const QString&,
                         NXOpen::Annotations::PointTarget*,
                         const bool& = false) const;

    bool dataPmiLineWeld(CADDataTable*,
                         const QString&,
                         const QString&,
                         NXOpen::Annotations::PmiLineWeld*,
                         const bool& = false) const;

    /* ------------------------------------------------------ */

    NXOpen::Session* _session;
    NXOpen::Part* _work_part;
    NXOpen::Part* _owning_part;
    NXOpen::Annotations::PmiManager* _pmi_manager;
    NXOpen::Annotations::Pmi* _pmi;
    tag_t _part_tag;
    tag_t _pmi_tag;

    /// @warning: Only used for debugging!
    static unsigned int _PmiObjectCounter;
};


#endif // define __ug_pmi_root_class__h__
// vim:ts=8:sw=4:cindent
