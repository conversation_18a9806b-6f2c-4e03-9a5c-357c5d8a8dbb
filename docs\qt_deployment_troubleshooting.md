# Qt6 Deployment Troubleshooting Guide

## Overview

This guide helps resolve common issues when loading the `tw_demo.dll` with Qt6 dependencies in NX.

## Common Error Messages

### "Failed to load image - see log file for details"

This error typically occurs when Qt6 runtime libraries are missing or cannot be found.

**Solution**: Deploy Qt6 libraries to the same directory as `tw_demo.dll`

## Deployment Methods

### Method 1: Automatic Deployment (Recommended)

The project is configured to automatically deploy Qt libraries when building:

```bash
cmake --build build --config Debug
```

This will automatically run `windeployqt` after building the DLL.

### Method 2: Manual Deployment using <PERSON>ch Script

Run the provided batch script:

```bash
deploy_qt.bat
```

### Method 3: Manual Deployment using windeployqt

For Debug version:
```bash
D:\Qt\6.9.0\msvc2022_64\bin\windeployqt.exe --debug --dir "build\Debug" "build\Debug\tw_demo.dll"
```

For Release version:
```bash
D:\Qt\6.9.0\msvc2022_64\bin\windeployqt.exe --release --dir "build\Release" "build\Release\tw_demo.dll"
```

## Required Qt6 Libraries

After deployment, the following files should be present in the same directory as `tw_demo.dll`:

### Core Libraries (Debug)
- `Qt6Cored.dll` - Core Qt functionality
- `Qt6Guid.dll` - GUI functionality  
- `Qt6Widgetsd.dll` - Widget functionality
- `Qt6Networkd.dll` - Network functionality (for plugins)
- `Qt6Svgd.dll` - SVG support (for icons)

### Core Libraries (Release)
- `Qt6Core.dll`
- `Qt6Gui.dll`
- `Qt6Widgets.dll`
- `Qt6Network.dll`
- `Qt6Svg.dll`

### Additional Files
- `opengl32sw.dll` - Software OpenGL renderer
- `D3Dcompiler_47.dll` - DirectX shader compiler
- `dxcompiler.dll` - DirectX compiler (newer versions)
- `dxil.dll` - DirectX intermediate language

### Plugin Directories
- `platforms/` - Platform-specific plugins (qwindowsd.dll)
- `styles/` - Style plugins (qmodernwindowsstyled.dll)
- `imageformats/` - Image format plugins (qjpegd.dll, qsvgd.dll, etc.)
- `iconengines/` - Icon engine plugins (qsvgicond.dll)
- `generic/` - Generic plugins
- `networkinformation/` - Network information plugins
- `tls/` - TLS/SSL plugins

## Verification Steps

### 1. Check File Presence

Verify that Qt libraries are present:

```bash
dir build\Debug\Qt6*.dll
```

Expected output should show Qt6 DLL files.

### 2. Check Plugin Directories

Verify that plugin directories exist:

```bash
dir build\Debug\platforms
dir build\Debug\styles
```

### 3. Dependency Walker (Advanced)

Use Dependency Walker to check for missing dependencies:

1. Download Dependency Walker (depends.exe)
2. Open `tw_demo.dll` in Dependency Walker
3. Look for any missing DLLs marked in red

## Common Issues and Solutions

### Issue 1: "Qt platform plugin could not be initialized"

**Cause**: Missing platform plugins or Qt cannot find them

**Solutions**:
1. Ensure `platforms/qwindowsd.dll` (Debug) or `platforms/qwindows.dll` (Release) is present
2. Check that `qt.conf` file exists in the same directory as `tw_demo.dll`
3. Verify the `qt.conf` file contains:
   ```
   [Paths]
   Plugins = .
   ```
4. Set environment variable manually: `set QT_PLUGIN_PATH=path\to\dll\directory`
5. Enable debug output: `set QT_DEBUG_PLUGINS=1` to see what Qt is trying to load

### Issue 2: "Cannot load library Qt6Core"

**Cause**: Qt6Core.dll not found or wrong version

**Solution**: 
1. Re-run windeployqt
2. Check that the correct Debug/Release version is deployed
3. Verify Qt6 installation path

### Issue 3: Visual C++ Runtime Missing

**Cause**: Missing Visual C++ Redistributable

**Solution**: Install Visual C++ Redistributable 2022 (x64)

### Issue 4: OpenGL Issues

**Cause**: Missing OpenGL libraries

**Solution**: Ensure `opengl32sw.dll` is deployed (software renderer fallback)

## Environment Variables

### QT_PLUGIN_PATH

If plugins are not found, you can set the plugin path:

```bash
set QT_PLUGIN_PATH=D:\dev\2025\tw_demo\build\Debug
```

### QT_DEBUG_PLUGINS

For debugging plugin loading issues:

```bash
set QT_DEBUG_PLUGINS=1
```

## Testing the Deployment

### 1. Simple Test

Try loading the DLL in NX after deployment.

### 2. Command Line Test

Test the DLL dependencies from command line:

```bash
cd build\Debug
dumpbin /dependents tw_demo.dll
```

### 3. Process Monitor

Use Process Monitor (ProcMon) to see which files NX is trying to access when loading the DLL.

## Build Configuration Notes

### Debug vs Release

- Debug builds require Debug Qt libraries (ending with 'd')
- Release builds require Release Qt libraries (without 'd')
- Make sure to deploy the correct version

### Architecture

- Ensure all libraries are 64-bit (x64) to match NX
- Mixed architectures will cause loading failures

## Alternative Solutions

### 1. Static Linking

Consider static linking Qt if deployment becomes problematic:

```cmake
set(Qt6_USE_STATIC_LIBS ON)
```

### 2. Qt Installation Path

Add Qt6 bin directory to system PATH:

```
D:\Qt\6.9.0\msvc2022_64\bin
```

## Getting Help

If issues persist:

1. Check NX log files for detailed error messages
2. Use Windows Event Viewer for system-level errors
3. Enable Qt debug output with environment variables
4. Test with a minimal Qt application first

## Useful Commands

```bash
# List all DLL dependencies
dumpbin /dependents tw_demo.dll

# Check if DLL is valid
dumpbin /headers tw_demo.dll

# Verify Qt deployment
windeployqt --list source tw_demo.dll
```
