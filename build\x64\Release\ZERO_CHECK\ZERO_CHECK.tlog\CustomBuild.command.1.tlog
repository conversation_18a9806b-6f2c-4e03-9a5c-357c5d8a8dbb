^D:\DEV\2025\TW_DEMO\BUILD\CMAKEFILES\DD2061ABE8C99B1A8779A6496F15D226\GENERATE.STAMP.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/dev/2025/tw_demo -BD:/dev/2025/tw_demo/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file D:/dev/2025/tw_demo/build/tw_demo.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
