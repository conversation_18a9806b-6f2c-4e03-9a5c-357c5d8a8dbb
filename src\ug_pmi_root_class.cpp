//  -*- mode: c++ -*-

/// @file     "ug_pmi_root_class.cpp"
///
/// @brief    TODO
///
/// <AUTHOR> <<EMAIL>>
/// @date     2010-10-28
/// @revision $Id$
//////////////////////////////////////////////////////////////////////////////

#include "ug_pmi_root_class.h"

// System includes
#include <cstdio>

// Qt includes
#include <QFileInfo>
#include <QRegExp>
#include <QStringList>
#include <QVector>

// BCT includes
#include "bct_logging.h"
#include "nx_open.h"
#include "ug_object.h"
#include "ug_part.h"

// Local includes
#include "bct_inspector_ns.h"
#include "cad_data_item.h"
#include "cad_data_line.h"
#include "cad_data_table.h"
#include "chx_stamp_look.h"
#include "cn_item.h"
#include "configuration_root.h"
#include "ug_annotation_root_class.h" // - Remove it, if ...
#include "ug_dimension_root_class.h"  // - Remove it, if ...
#include "ug_obj_id.h"

// UG Open C includes
#include <uf_assem.h>
#include <uf_disp.h> // Only used for "UF_DISP_add_item_to_display(...)", replace it by NX Open C++
#include <uf_drf.h>
#include <uf_obj.h>

// NX Open C++ includes
#include <NXOpen/Annotations.hxx>
#include <NXOpen/Annotations_AnnotationManager.hxx>
#include <NXOpen/Annotations_AssociatedObject.hxx>
#include <NXOpen/Annotations_AssociativeText.hxx>
#include <NXOpen/Annotations_BalloonNote.hxx>
#include <NXOpen/Annotations_BalloonNoteBuilder.hxx>
#include <NXOpen/Annotations_CompanyProprietaryInformation.hxx>
#include <NXOpen/Annotations_CoordinateNote.hxx>
#include <NXOpen/Annotations_CustomSymbolCollection.hxx>
#include <NXOpen/Annotations_Datum.hxx>
#include <NXOpen/Annotations_Dimension.hxx>
#include <NXOpen/Annotations_DimensionPreferences.hxx>
#include <NXOpen/Annotations_DraftingDatumFeatureSymbolBuilder.hxx>
#include <NXOpen/Annotations_EnterpriseIdentification.hxx>
#include <NXOpen/Annotations_ExportControl.hxx>
#include <NXOpen/Annotations_Fcf.hxx>
#include <NXOpen/Annotations_GdtDatumCollection.hxx>
#include <NXOpen/Annotations_GeneralNote.hxx>
#include <NXOpen/Annotations_GeneralNoteBuilder.hxx>
#include <NXOpen/Annotations_GovernmentSecurityInformation.hxx>
#include <NXOpen/Annotations_LetteringPreferences.hxx>
#include <NXOpen/Annotations_LineWeldDataBuilder.hxx>
#include <NXOpen/Annotations_LocatorDesignator.hxx>
#include <NXOpen/Annotations_MasterSymbolListItemBuilder.hxx>
#include <NXOpen/Annotations_MasterSymbolListItemBuilderList.hxx>
#include <NXOpen/Annotations_MaterialSpecification.hxx>
#include <NXOpen/Annotations_PartIdentification.hxx>
#include <NXOpen/Annotations_Pmi.hxx>
#include <NXOpen/Annotations_PmiArcLengthDimension.hxx>
#include <NXOpen/Annotations_PmiAttribute.hxx>
#include <NXOpen/Annotations_PmiAttributeBuilder.hxx>
#include <NXOpen/Annotations_PmiAttributeCollection.hxx>
#include <NXOpen/Annotations_PmiBaselineDimension.hxx>
#include <NXOpen/Annotations_PmiChainDimension.hxx>
#include <NXOpen/Annotations_PmiChamferDimension.hxx>
#include <NXOpen/Annotations_PmiCollection.hxx>
#include <NXOpen/Annotations_PmiConcentricCircleDimension.hxx>
#include <NXOpen/Annotations_PmiCustomSymbol.hxx>
#include <NXOpen/Annotations_PmiCustomSymbolBuilder.hxx>
#include <NXOpen/Annotations_PmiCylindricalDimension.hxx>
#include <NXOpen/Annotations_PmiDatumTargetBuilder.hxx>
#include <NXOpen/Annotations_PmiDiameterDimension.hxx>
#include <NXOpen/Annotations_PmiFilterByView.hxx>
#include <NXOpen/Annotations_PmiFilterCollection.hxx>
#include <NXOpen/Annotations_PmiFoldedRadiusDimension.hxx>
#include <NXOpen/Annotations_PmiHoleDimension.hxx>
#include <NXOpen/Annotations_PmiHorizontalDimension.hxx>
#include <NXOpen/Annotations_PmiHorizontalOrdinateDimension.hxx>
#include <NXOpen/Annotations_PmiInteger.hxx>
// Deprecated in NX6.0.0 ->
#include <NXOpen/Annotations_PmiLabel.hxx>
// <- Deprecated in NX6.0.0
#include <NXOpen/Annotations_PmiLineWeld.hxx>
#include <NXOpen/Annotations_PmiLineWeldBuilder.hxx>
#include <NXOpen/Annotations_PmiMajorAngularDimension.hxx>
#include <NXOpen/Annotations_PmiManager.hxx>
#include <NXOpen/Annotations_PmiMinorAngularDimension.hxx>
#include <NXOpen/Annotations_PmiNote.hxx>
#include <NXOpen/Annotations_PmiNumber.hxx>
#include <NXOpen/Annotations_PmiOrdinateOriginDimension.hxx>
#include <NXOpen/Annotations_PmiParallelDimension.hxx>
#include <NXOpen/Annotations_PmiPerpendicularDimension.hxx>
#include <NXOpen/Annotations_PmiRadiusDimension.hxx>
#include <NXOpen/Annotations_PmiString.hxx>
#include <NXOpen/Annotations_PmiUrlNote.hxx>
#include <NXOpen/Annotations_PmiUrlNoteBuilder.hxx>
#include <NXOpen/Annotations_PmiUserDefined.hxx>
#include <NXOpen/Annotations_PmiVerticalDimension.hxx>
#include <NXOpen/Annotations_PmiVerticalOrdinateDimension.hxx>
#include <NXOpen/Annotations_PointTarget.hxx>
#include <NXOpen/Annotations_ProcessSpecification.hxx>
#include <NXOpen/Annotations_SimpleDraftingAid.hxx>
#include <NXOpen/Annotations_SpecificNote.hxx>
#include <NXOpen/Annotations_SurfaceFinish.hxx>
#include <NXOpen/Annotations_SurfaceFinishBuilder.hxx>
#include <NXOpen/Annotations_SymbolPreferences.hxx>
#include <NXOpen/Annotations_WeldCollection.hxx>
#include <NXOpen/Assemblies_Component.hxx>
#include <NXOpen/Assemblies_ComponentAssembly.hxx>
#include <NXOpen/DisplayManager.hxx>
#include <NXOpen/DisplayableObject.hxx>
#include <NXOpen/Edge.hxx>
#include <NXOpen/Face.hxx>
#include <NXOpen/Layer.hxx>
#include <NXOpen/Layer_LayerManager.hxx>
#include <NXOpen/ListingWindow.hxx>
#include <NXOpen/ModelingView.hxx>
#include <NXOpen/ModelingViewCollection.hxx>
#include <NXOpen/NXException.hxx>
#include <NXOpen/NXObject.hxx>
#include <NXOpen/NXObjectManager.hxx>
#include <NXOpen/NXString.hxx>
#include <NXOpen/Part.hxx>
#include <NXOpen/PartCollection.hxx>
#include <NXOpen/Session.hxx>
#include <NXOpen/TaggedObject.hxx>
#include <NXOpen/View.hxx>
#include <NXOpen/ugmath.hxx>

#include "../../../../../../../libraries/qt4/coreutils/appcontext.h"


/// @warning: Only used for debugging!
/*static*/ unsigned int UGPmiRootClass::_PmiObjectCounter = 0;


/**
 * Basic Constructor
 */
UGPmiRootClass::UGPmiRootClass(const tag_t& pmiTag, const tag_t& partTag /* = NULL_TAG*/) {
    // ---------------------------------------------------------
    // TODO (SS): Only used for debugging!
    // ---------------------------------------------------------
    _PmiObjectCounter += (_PmiObjectCounter + 1 > 0 ? 1 : 0);
    _session     = 0;
    _work_part   = 0;
    _owning_part = 0;
    _pmi_manager = 0;
    _pmi         = 0;
    _part_tag    = NULL_TAG;
    _pmi_tag     = NULL_TAG;

    auto listingWindow = NXOpen::Session::GetSession()->ListingWindow();
    listingWindow->Open();

    if (pmiTag != NULL_TAG && UGPmiRootClass::isPmi(pmiTag)) {
        if (NXOpen::Session* session = NXOpen::Session::GetSession()) {
            if (NXOpen::Part* workPart = session->Parts()->Work()) {
                if (NXOpen::Annotations::PmiManager* pmiManager = workPart->PmiManager()) {
                    /* ------------------------------------------------------ */

                    NXOpen::Annotations::Pmi*        pmi        = 0;
                    NXOpen::Annotations::Annotation* annotation = 0;
                    if (NXOpen::TaggedObject* pmiTaggedObj = NXOpen::NXObjectManager::Get(pmiTag)) {
                        if (dynamic_cast<NXOpen::Annotations::Pmi*>(pmiTaggedObj)) {
                            //Q_DEBUG("Input is a: NXOpen::Annotations::Pmi");
                            pmi = dynamic_cast<NXOpen::Annotations::Pmi*>(pmiTaggedObj);
                        }
                        else if (dynamic_cast<NXOpen::Annotations::PmiArcLengthDimension*>(pmiTaggedObj) || dynamic_cast<NXOpen::Annotations::PmiBaselineDimension*>(pmiTaggedObj) || dynamic_cast<NXOpen::Annotations::PmiChainDimension*>(pmiTaggedObj) || dynamic_cast<NXOpen::Annotations::PmiChamferDimension*>(pmiTaggedObj) || dynamic_cast<NXOpen::Annotations::PmiConcentricCircleDimension*>(pmiTaggedObj) || dynamic_cast<NXOpen::Annotations::PmiCylindricalDimension*>(pmiTaggedObj) || dynamic_cast<NXOpen::Annotations::PmiDiameterDimension*>(pmiTaggedObj) || dynamic_cast<NXOpen::Annotations::PmiFoldedRadiusDimension*>(pmiTaggedObj) || dynamic_cast<NXOpen::Annotations::PmiHoleDimension*>(pmiTaggedObj) || dynamic_cast<NXOpen::Annotations::PmiHorizontalDimension*>(pmiTaggedObj) || dynamic_cast<NXOpen::Annotations::PmiHorizontalOrdinateDimension*>(pmiTaggedObj) || dynamic_cast<NXOpen::Annotations::PmiMajorAngularDimension*>(pmiTaggedObj) || dynamic_cast<NXOpen::Annotations::PmiMinorAngularDimension*>(pmiTaggedObj) || dynamic_cast<NXOpen::Annotations::PmiOrdinateOriginDimension*>(pmiTaggedObj) || dynamic_cast<NXOpen::Annotations::PmiParallelDimension*>(pmiTaggedObj) || dynamic_cast<NXOpen::Annotations::PmiPerpendicularDimension*>(pmiTaggedObj) || dynamic_cast<NXOpen::Annotations::PmiRadiusDimension*>(pmiTaggedObj) || dynamic_cast<NXOpen::Annotations::PmiVerticalDimension*>(pmiTaggedObj) || dynamic_cast<NXOpen::Annotations::PmiVerticalOrdinateDimension*>(pmiTaggedObj) || dynamic_cast<NXOpen::Annotations::Fcf*>(pmiTaggedObj) || dynamic_cast<NXOpen::Annotations::Datum*>(pmiTaggedObj) || dynamic_cast<NXOpen::Annotations::PmiCustomSymbol*>(pmiTaggedObj) || dynamic_cast<NXOpen::Annotations::PmiNote*>(pmiTaggedObj) || dynamic_cast<NXOpen::Annotations::PmiAttribute*>(pmiTaggedObj) || dynamic_cast<NXOpen::Annotations::PointTarget*>(pmiTaggedObj) || dynamic_cast<NXOpen::Annotations::PmiLineWeld*>(pmiTaggedObj)) {
                            listingWindow->WriteLine("Input is a: NXOpen::Annotations::Annotation");
                            annotation = dynamic_cast<NXOpen::Annotations::Annotation*>(pmiTaggedObj);
                        }
                        else if (dynamic_cast<NXOpen::Annotations::PmiLabel*>(pmiTaggedObj)) {
                            // Deprecated in NX6.0.0 ->
                            static bool log_deprecated = true;
                            if (log_deprecated) {
                                log_deprecated = false;
                                listingWindow->WriteLine("Found deprecated object type, PMI-Label-Objects are deprecated since NX6.0.0.");
                            }
                            listingWindow->WriteLine("Input is a: NXOpen::Annotations::Annotation");
                            annotation = dynamic_cast<NXOpen::Annotations::Annotation*>(pmiTaggedObj);
                            // <- Deprecated in NX6.0.0
                        }
                    }

                    if (annotation) {
                        if (pmiManager->IsInheritedPmi(annotation)) {
                            listingWindow->WriteLine("Ask for the parent of an inherited PMI.");
                            annotation = pmiManager->GetInheritParent(annotation);
                        }

                        if (annotation)
                            pmi = pmiManager->GetDisplayInstanceParent(annotation);
                    }

                    if (pmi) {
                        _session     = session;
                        _work_part   = workPart;
                        _owning_part = workPart;
                        _pmi_manager = pmiManager;
                        _pmi         = pmi;
                        _part_tag    = partTag;
                        _pmi_tag     = pmi->Tag();
                        //Q_DEBUG("Collect Pmi data of : " << _pmi_tag << "(" << _pmi->Index() << " - "
                        //                                 << QSTRING_NX(_pmi->Name())
                        //                                 << ")"
                        //                                 << " - Type: "
                        //                                 << Qt4UgOpen::UGObject(_pmi_tag).type()
                        //                                 << " - Sub Type: "
                        //                                 << Qt4UgOpen::UGObject(_pmi_tag).subtype());
                        //if (pmiTag != _pmi_tag)
                        //    Q_DEBUG("Based on : " << pmiTag << "(" << Qt4UgOpen::UGObject(pmiTag).name()
                        //                          << ")"
                        //                          << " - Type: "
                        //                          << Qt4UgOpen::UGObject(pmiTag).type()
                        //                          << " - Sub Type: "
                        //                          << Qt4UgOpen::UGObject(pmiTag).subtype());

                        if (workPart != dynamic_cast<NXOpen::Part*>(pmi->OwningPart())) {
                            listingWindow->WriteLine("Pmi is not part of the work part!");
                            std::vector<NXOpen::Annotations::Annotation*> stdVecector = pmi->GetDisplayInstances();
                            if (stdVecector.empty() || !dynamic_cast<NXOpen::Annotations::PmiLineWeld*>(stdVecector[0])) {
                                // ---------------------------------------------------------
                                // TODO (SS): Currently we only support "PmiLineWeld",
                                //            for more details see the user story "Q180.2".
                                // ---------------------------------------------------------
                            }
                            //else if (!config()->cfg().useAtAutoCreateComponentPmi() && !config()->cfg().useAtManualCreateComponentPmi()) {
                            //    // ---------------------------------------------------------
                            //    // TODO (SS): Currently we need the following piece of code
                            //    //            only for component PMIs, and that is the reason
                            //    //            why we combine it with the configuration. One
                            //    //            day if we want to support assemblies in general
                            //    //            we have to remove the configuration check!
                            //    // ---------------------------------------------------------
                            //}
                            else if (NXOpen::Part* owningPart = dynamic_cast<NXOpen::Part*>(pmi->OwningPart())) {
                                _owning_part = owningPart;
                                if (NXOpen::Annotations::PmiManager* owningPmiManager = owningPart->PmiManager())
                                    _pmi_manager = owningPmiManager;
                                else
                                    listingWindow->WriteLine("The owning part pmi manager is invalid.");
                            }
                            else {
                                listingWindow->("The owning part is invalid.");
                            }
                        }
                    }
                    else {
                        listingWindow->CloseWindow("The given object is not a PMI-Object.");
                    }

                    /* ------------------------------------------------------ */
                }
                else {
                    listingWindow->WriteLine("The current pmi manager is invalid.");
                }
            }
            else {
                listingWindow->WriteLine("The current work part is invalid.");
            }
        }
        else {
            listingWindow->WriteLine("The current session is invalid.");
        }
    }
}


/**
 * Copy Operator
 */
UGPmiRootClass& UGPmiRootClass::operator=(const UGPmiRootClass& copy)
{
    _session = copy._session;
    _work_part = copy._work_part;
    _owning_part = copy._owning_part;
    _pmi_manager = copy._pmi_manager;
    _pmi = copy._pmi;
    _part_tag = copy._part_tag;
    _pmi_tag = copy._pmi_tag;
    return (*this);
}


/**
 *
 */
bool UGPmiRootClass::isPmi(void) const
{
    bool isPmi = false;

    // Speed up, don't check things twice.
    isPmi = (_pmi != 0);

    // This test is for all versions useful. In case the
    // constructor fails during the type estimation, it
    // double checks the result.
    if (!isPmi && _pmi_tag != NULL_TAG)
        isPmi = UGPmiRootClass::isPmi(_pmi_tag);
    return isPmi;
}


/**
 *
 */
/*static*/ bool UGPmiRootClass::isPmi(const tag_t& tag)
{
    return true;
    //bool is_pmi = false;

    //if (tag != NULL_TAG) {
    //    switch (Qt4UgOpen::UGObject(tag).type()) {

    //    /* ------------------------------------------------------ */

    //    case UF_drafting_entity_type: // 25
    //    case UF_dimension_type:       // 26
    //        if (tag_t t = (!UF_ASSEM_is_occurrence(tag) ? tag : UF_ASSEM_ask_prototype_of_occ(tag))) {

    //            logical is_instance = false;
    //            //if (UG_FAILURE(UF_DRF_is_pmi_display_instance(t, &is_instance)))
    //            //    is_instance = false;

    //            if (!(is_pmi = is_instance)) {
    //                logical is_inherited = false;
    //                if (UG_SUCCESS(UF_DRF_is_inherited_pmi(t, &is_inherited)) && is_inherited) {
    //                    tag_t p = NULL_TAG;
    //                    if (UG_SUCCESS(UF_DRF_ask_parent_of_inherited_pmi(tag, &p)) && p != NULL_TAG)
    //                        is_pmi = true;
    //                }
    //            }
    //        }
    //        break;

    //    /* ------------------------------------------------------ */

    //    case UF_smsp_product_definition_type: // 155 // PMIs
    //        switch (Qt4UgOpen::UGObject(tag).subtype()) {
    //        case UF_smsp_product_attribute_subtype: // 4
    //            is_pmi = true;
    //            break;
    //        }
    //        break;

    //    /* ------------------------------------------------------ */

    //    case UF_smart_model_instance_type: // 158 // PMI Instances
    //        switch (Qt4UgOpen::UGObject(tag).subtype()) {
    //        case UF_combined_attribute_instance_subtype: // 0
    //            is_pmi = true;
    //            break;
    //        }
    //        break;

    //    /* ------------------------------------------------------ */

    //    case UF_pattern_type:               //  10 // Pattern
    //    case UF_tabular_note_type:          // 165 // Tables
    //    case UF_tol_feature_instance_type:  // 248 // GD&T Instances
    //    case UF_tolerance_feature_type:     // 251 // GD&Ts
    //    case UF_feature_control_frame_type: // 254 // GD&Ts (old style)
    //    default:
    //        // is_pmi = false;
    //        break;
    //    }
    //}

    //return is_pmi;
}


/**
 *
 */
bool UGPmiRootClass::isPmiBalloon(std::string* balloonText /* = 0*/) const {
    bool isPmi = false;

    if (balloonText)
        (*balloonText) = "";

    if (_session && _work_part && _owning_part && _pmi_manager && _pmi) {
        std::vector<NXOpen::Annotations::Annotation*> dispInst(displayInstances());
        if (!dispInst.empty() && dispInst[0]) {
            if (NXOpen::Annotations::BalloonNote* note = dynamic_cast<NXOpen::Annotations::BalloonNote*>(dispInst[0])) {
                isPmi = true;
                if (balloonText) {
                    //log << "Try to get the balloon text.";
                    if (NXOpen::Annotations::BalloonNoteBuilder* noteBuilder = _pmi_manager->PmiAttributes()->CreateBalloonNoteBuilder(note)) {
                        (*balloonText) = noteBuilder->BalloonText();
                        // noteBuilder->Commit(); // Nothing changed, no commit required!
                        noteBuilder->Destroy();
                        //log << "Extracted balloon text: " << *balloonText;
                    }
                    else {
                        //log << "The creation of the balloon note builder has failed.";
                    }
                }
            }
        }
    }
    return isPmi;
}


/**
 *
 */
bool UGPmiRootClass::isPmiToUse(const bool& manualSelection /* = false*/) const {
    bool toUse = false;
    return true;
#if 0
    if (_session && _work_part && _owning_part && _pmi_manager && _pmi) {
        std::vector<NXOpen::Annotations::Annotation*> dispInst(displayInstances());
        if (!dispInst.empty() && dispInst[0]) {
            if (NXOpen::Annotations::Dimension* dimension = dynamic_cast<NXOpen::Annotations::Dimension*>(dispInst[0])) {
                if (manualSelection && config()->cfg().useAtManualCreatePmi()) {
                    toUse = true;
                }
                else if (!manualSelection && config()->cfg().useAtAutoCreatePmi()) {
                    QList<ConfigurationXML::sOBJECT_FILTER_ADDON> addon;
                    addon << ConfigurationXML::sOBJECT_FILTER_ADDON(
                            KEY_2101,
                            "0.0"); // We guess we will get a value!
                    addon << ConfigurationXML::sOBJECT_FILTER_ADDON(
                            KEY_2501_b,
                            (int(dimension->InspectionDimensionFlag() ? K2501::DIMB_WITH_INSPECTION
                                                                      : K2501::DIMB_NONE_INSPECTION)));
                    switch (dimension->ToleranceType()) {
                    case NXOpen::Annotations::ToleranceTypeBasic: // [xxx]
                        addon << ConfigurationXML::sOBJECT_FILTER_ADDON(KEY_2501,
                                                                        K2501::DIM_ATTRIB_BASIC);
                        addon << ConfigurationXML::sOBJECT_FILTER_ADDON(KEY_2503, K2503::DIM_TYPE_MODEL);
                        // addon << ConfigurationXML::sOBJECT_FILTER_ADDON(KEY_XXXX, KXXXX::DIM_SCALE);
                        break;
                    case NXOpen::Annotations::ToleranceTypeReference: // (xxx)
                        addon << ConfigurationXML::sOBJECT_FILTER_ADDON(KEY_2501,
                                                                        K2501::DIM_ATTRIB_PRUEF);
                        addon << ConfigurationXML::sOBJECT_FILTER_ADDON(KEY_2503,
                                                                        K2503::DIM_TYPE_REFERENCE);
                        // addon << ConfigurationXML::sOBJECT_FILTER_ADDON(KEY_XXXX, KXXXX::DIM_SCALE);
                        break;
                    case NXOpen::Annotations::ToleranceTypeNotToScale: // _xxx_
                        addon << ConfigurationXML::sOBJECT_FILTER_ADDON(KEY_2501,
                                                                        K2501::DIM_ATTRIB_PRUEF);
                        addon << ConfigurationXML::sOBJECT_FILTER_ADDON(KEY_2503, K2503::DIM_TYPE_MODEL);
                        // addon << ConfigurationXML::sOBJECT_FILTER_ADDON(KEY_XXXX,
                        // KXXXX::DIM_NOT_SCALE);
                        break;
                    case NXOpen::Annotations::ToleranceTypeDiameterReference: // (DIA xxx)
                        addon << ConfigurationXML::sOBJECT_FILTER_ADDON(KEY_2501,
                                                                        K2501::DIM_ATTRIB_PRUEF);
                        addon << ConfigurationXML::sOBJECT_FILTER_ADDON(KEY_2503,
                                                                        K2503::DIM_TYPE_REFERENCE);
                        // addon << ConfigurationXML::sOBJECT_FILTER_ADDON(KEY_XXXX, KXXXX::DIM_SCALE);
                        break;
                    case NXOpen::Annotations::ToleranceTypeBasicNotToScale: // _[xxx]_
                        addon << ConfigurationXML::sOBJECT_FILTER_ADDON(KEY_2501,
                                                                        K2501::DIM_ATTRIB_BASIC);
                        addon << ConfigurationXML::sOBJECT_FILTER_ADDON(KEY_2503, K2503::DIM_TYPE_MODEL);
                        // addon << ConfigurationXML::sOBJECT_FILTER_ADDON(KEY_XXXX,
                        // KXXXX::DIM_NOT_SCALE);
                        break;
                    default:
                        addon << ConfigurationXML::sOBJECT_FILTER_ADDON(KEY_2501,
                                                                        K2501::DIM_ATTRIB_PRUEF);
                        addon << ConfigurationXML::sOBJECT_FILTER_ADDON(KEY_2503, K2503::DIM_TYPE_MODEL);
                        // addon << ConfigurationXML::sOBJECT_FILTER_ADDON(KEY_XXXX, KXXXX::DIM_SCALE);
                        break;
                    }
                    toUse = config()->cfg().useAtAutoCreate(
                            QList<ChxType>() << ChxType::LIN << ChxType::RAD << ChxType::DIA << ChxType::ANG
                                             << ChxType::DEPTH
                                             << ChxType::CHAMFER,
                            addon);
                }
            }
            else if (dynamic_cast<NXOpen::Annotations::Fcf*>(dispInst[0])) {
                toUse = ((manualSelection && config()->cfg().useAtManualCreatePmi()) || (!manualSelection && config()->cfg().useAtAutoCreatePmi() && config()->cfg().useAtAutoCreate(QList<ChxType>() << ChxType::STRAIGHTNESS << ChxType::FLATNESS << ChxType::CIRCULAR << ChxType::CYLINDRICAL << ChxType::PROFILE_OF_LINE << ChxType::PROFILE_OF_SURFACE << ChxType::ANGULARITY << ChxType::PERPENDICULARITY << ChxType::PARALLELISM << ChxType::POSITION << ChxType::CONCENTRICITY << ChxType::SYMMETRY << ChxType::CIRCULAR_RUNOUT << ChxType::TOTAL_RUNOUT)));
            }
            else if (dynamic_cast<NXOpen::Annotations::Datum*>(dispInst[0])) {
                toUse = ((manualSelection && config()->cfg().useAtManualCreatePmi()) || (!manualSelection && config()->cfg().useAtAutoCreatePmi() && config()->cfg().useAtAutoCreate(ChxType::DATUM_PLANE)));
            }
            else if (dynamic_cast<NXOpen::Annotations::PmiCustomSymbol*>(dispInst[0])) {
                toUse = ((manualSelection && config()->cfg().useAtManualCreatePmi()) || (!manualSelection && config()->cfg().useAtAutoCreatePmi() && config()->cfg().useAtAutoCreate(ChxType::SYMBOL)));

                // Deprecated in NX6.0.0 ->
            }
            else if (dynamic_cast<NXOpen::Annotations::PmiLabel*>(dispInst[0])) {
                toUse = ((manualSelection && config()->cfg().useAtManualCreatePmi()) || (!manualSelection && config()->cfg().useAtAutoCreatePmi() && config()->cfg().useAtAutoCreate(ChxType::NOTE_3D)));
                // <- Deprecated in NX6.0.0
            }
            else if (dynamic_cast<NXOpen::Annotations::PmiNote*>(dispInst[0])) {
                toUse = ((manualSelection && config()->cfg().useAtManualCreatePmi()) || (!manualSelection && config()->cfg().useAtAutoCreatePmi() && config()->cfg().useAtAutoCreate(ChxType::NOTE_3D)));
            }
            else if (NXOpen::Annotations::BalloonNote* attribute = dynamic_cast<NXOpen::Annotations::BalloonNote*>(dispInst[0])) {
                toUse = ((manualSelection && config()->cfg().useAtManualCreatePmi()) || (!manualSelection && config()->cfg().useAtAutoCreatePmi() && config()->cfg().useAtAutoCreate(ChxType::SYMBOL, ConfigurationXML::sOBJECT_FILTER_ADDON(), ConfigurationXML::sOBJECT_FILTER::EX_ID_SYMBOLS)));
            }
            else if (NXOpen::Annotations::CompanyProprietaryInformation* attribute = dynamic_cast<NXOpen::Annotations::CompanyProprietaryInformation*>(dispInst[0])) {
                toUse = ((manualSelection && config()->cfg().useAtManualCreatePmi()) || (!manualSelection && config()->cfg().useAtAutoCreatePmi()));
            }
            else if (NXOpen::Annotations::CoordinateNote* attribute = dynamic_cast<NXOpen::Annotations::CoordinateNote*>(dispInst[0])) {
                toUse = ((manualSelection && config()->cfg().useAtManualCreatePmi()) || (!manualSelection && config()->cfg().useAtAutoCreatePmi()));
            }
            else if (NXOpen::Annotations::EnterpriseIdentification* attribute = dynamic_cast<NXOpen::Annotations::EnterpriseIdentification*>(dispInst[0])) {
                toUse = ((manualSelection && config()->cfg().useAtManualCreatePmi()) || (!manualSelection && config()->cfg().useAtAutoCreatePmi()));
            }
            else if (NXOpen::Annotations::ExportControl* attribute = dynamic_cast<NXOpen::Annotations::ExportControl*>(dispInst[0])) {
                toUse = ((manualSelection && config()->cfg().useAtManualCreatePmi()) || (!manualSelection && config()->cfg().useAtAutoCreatePmi()));
            }
            else if (NXOpen::Annotations::GeneralNote* attribute = dynamic_cast<NXOpen::Annotations::GeneralNote*>(dispInst[0])) {
                toUse = ((manualSelection && config()->cfg().useAtManualCreatePmi()) || (!manualSelection && config()->cfg().useAtAutoCreatePmi()));
            }
            else if (NXOpen::Annotations::GovernmentSecurityInformation* attribute = dynamic_cast<NXOpen::Annotations::GovernmentSecurityInformation*>(dispInst[0])) {
                toUse = ((manualSelection && config()->cfg().useAtManualCreatePmi()) || (!manualSelection && config()->cfg().useAtAutoCreatePmi()));
            }
            else if (NXOpen::Annotations::LocatorDesignator* attribute = dynamic_cast<NXOpen::Annotations::LocatorDesignator*>(dispInst[0])) {
                toUse = ((manualSelection && config()->cfg().useAtManualCreatePmi()) || (!manualSelection && config()->cfg().useAtAutoCreatePmi()));
            }
            else if (NXOpen::Annotations::MaterialSpecification* attribute = dynamic_cast<NXOpen::Annotations::MaterialSpecification*>(dispInst[0])) {
                toUse = ((manualSelection && config()->cfg().useAtManualCreatePmi()) || (!manualSelection && config()->cfg().useAtAutoCreatePmi()));
            }
            else if (NXOpen::Annotations::PartIdentification* attribute = dynamic_cast<NXOpen::Annotations::PartIdentification*>(dispInst[0])) {
                toUse = ((manualSelection && config()->cfg().useAtManualCreatePmi()) || (!manualSelection && config()->cfg().useAtAutoCreatePmi()));
            }
            else if (NXOpen::Annotations::PmiInteger* attribute = dynamic_cast<NXOpen::Annotations::PmiInteger*>(dispInst[0])) {
                toUse = ((manualSelection && config()->cfg().useAtManualCreatePmi()) || (!manualSelection && config()->cfg().useAtAutoCreatePmi()));
            }
            else if (NXOpen::Annotations::PmiNumber* attribute = dynamic_cast<NXOpen::Annotations::PmiNumber*>(dispInst[0])) {
                toUse = ((manualSelection && config()->cfg().useAtManualCreatePmi()) || (!manualSelection && config()->cfg().useAtAutoCreatePmi()));
            }
            else if (NXOpen::Annotations::PmiString* attribute = dynamic_cast<NXOpen::Annotations::PmiString*>(dispInst[0])) {
                toUse = ((manualSelection && config()->cfg().useAtManualCreatePmi()) || (!manualSelection && config()->cfg().useAtAutoCreatePmi()));
            }
            else if (NXOpen::Annotations::PmiUrlNote* attribute = dynamic_cast<NXOpen::Annotations::PmiUrlNote*>(dispInst[0])) {
                toUse = ((manualSelection && config()->cfg().useAtManualCreatePmi()) || (!manualSelection && config()->cfg().useAtAutoCreatePmi()));
            }
            else if (NXOpen::Annotations::PmiUserDefined* attribute = dynamic_cast<NXOpen::Annotations::PmiUserDefined*>(dispInst[0])) {
                toUse = ((manualSelection && config()->cfg().useAtManualCreatePmi()) || (!manualSelection && config()->cfg().useAtAutoCreatePmi()));
            }
            else if (NXOpen::Annotations::ProcessSpecification* attribute = dynamic_cast<NXOpen::Annotations::ProcessSpecification*>(dispInst[0])) {
                toUse = ((manualSelection && config()->cfg().useAtManualCreatePmi()) || (!manualSelection && config()->cfg().useAtAutoCreatePmi()));
            }
            else if (NXOpen::Annotations::SpecificNote* attribute = dynamic_cast<NXOpen::Annotations::SpecificNote*>(dispInst[0])) {
                toUse = ((manualSelection && config()->cfg().useAtManualCreatePmi()) || (!manualSelection && config()->cfg().useAtAutoCreatePmi()));
            }
            else if (NXOpen::Annotations::SurfaceFinish* attribute = dynamic_cast<NXOpen::Annotations::SurfaceFinish*>(dispInst[0])) {
                toUse = ((manualSelection && config()->cfg().useAtManualCreatePmi()) || (!manualSelection && config()->cfg().useAtAutoCreatePmi()));
            }
            else if (dynamic_cast<NXOpen::Annotations::PointTarget*>(dispInst[0])) {
                toUse = ((manualSelection && config()->cfg().useAtManualCreatePmi()) || (!manualSelection && config()->cfg().useAtAutoCreatePmi() && config()->cfg().useAtAutoCreate(ChxType::DATUM_PLANE)));
            }
            else if (dynamic_cast<NXOpen::Annotations::PmiLineWeld*>(dispInst[0])) {
                toUse = ((manualSelection && config()->cfg().useAtManualCreatePmi()) || (!manualSelection && config()->cfg().useAtAutoCreatePmi() && config()->cfg().useAtAutoCreate(ChxType::WELD_SYMBOL)));
            }
            else {
                log.warning("The type of the given object is unknown.") << debugInfo();
                toUse = (manualSelection && config()->cfg().useAtManualCreatePmi());
            }
        }
    }
    return toUse;
#endif
}


/**
 *
 */
bool UGPmiRootClass::isPmiInChildOfWorkPart(void) const
{
    return true;
    //UG_INITIALIZE;
    //Q_DEBUG_SCOPE log;
    //bool isPmi = false;

    //NXOPEN_TRY
    //{
    //    if (_session && _work_part && _owning_part && _pmi_manager && _pmi) {

    //        /* ------------------------------------------------------ */

    //        if (_work_part != _owning_part) {
    //            if (NXOpen::Assemblies::ComponentAssembly* componentAssembly
    //                = _work_part->ComponentAssembly()) {
    //                if (NXOpen::Assemblies::Component* rootComponent = componentAssembly->RootComponent()) {
    //                    std::vector<NXOpen::Assemblies::Component*> children = rootComponent->GetChildren();
    //                    for (int i = 0; (i < (int)children.size() && !isPmi); i++) {
    //                        if (children[i]
    //                            && (dynamic_cast<NXOpen::Part*>(children[i]->Prototype()) == _owning_part))
    //                            isPmi = true;
    //                    }
    //                }
    //            }
    //        } else {
    //            log << "The owning part of the pmi is the work part.";
    //        }

    //        /* ------------------------------------------------------ */

    //    } else {
    //        log << "The object is not fully initialized.";
    //    }
    //}
    //NXOPEN_CATCH

    //return isPmi;
}


/**
 *
 */
tag_t UGPmiRootClass::tag(void) const
{
    //UG_INITIALIZE;
    // Q_DEBUG_SCOPE log;
    tag_t tag = _pmi_tag;

    if (_pmi)
        tag = _pmi->Tag();

    return tag;
}


/**
 *
 */
///*static*/ Qt4UgOpen::TagList UGPmiRootClass::componentPmis(const tag_t& tag,
//                                                            const bool& manualSelection /* = false*/,
//                                                            const bool& isPmiToUse /* = true*/)
//{
//    UG_INITIALIZE;
//    // Q_DEBUG_SCOPE log;
//    Qt4UgOpen::TagList list;
//
//    if (tag != NULL_TAG) {
//        NXOPEN_TRY
//        {
//            if (NXOpen::Session* session = NXOpen::Session::GetSession()) {
//                if (NXOpen::Part* workPart = session->Parts()->Work()) {
//                    if (NXOpen::Annotations::PmiManager* pmiManager = workPart->PmiManager()) {
//
//                        /* ------------------------------------------------------ */
//
//                        if (NXOpen::TaggedObject* pmiTaggedObj = NXOpen::NXObjectManager::Get(tag)) {
//                            if (NXOpen::Assemblies::Component* component
//                                = dynamic_cast<NXOpen::Assemblies::Component*>(pmiTaggedObj)) {
//                                if (NXOpen::Part* componentPart
//                                    = dynamic_cast<NXOpen::Part*>(component->Prototype())) {
//                                    if (NXOpen::Annotations::PmiManager* componentPmiManager
//                                        = componentPart->PmiManager()) {
//                                        if (NXOpen::Annotations::PmiCollection* componentPmis
//                                            = componentPmiManager->Pmis()) {
//                                            for (NXOpen::Annotations::PmiCollection::iterator it
//                                                 = componentPmis->begin();
//                                                 it != componentPmis->end();
//                                                 it++) {
//                                                // ---------------------------------------------------------
//                                                // TODO (SS): Currently we only support "PmiLineWeld",
//                                                //            for more details see the user story "Q180.2".
//                                                // ---------------------------------------------------------
//                                                if (*it) {
//                                                    std::vector<NXOpen::Annotations::Annotation*> stdVecector
//                                                        = (*it)->GetDisplayInstances();
//                                                    if (stdVecector.empty()
//                                                        || !dynamic_cast<NXOpen::Annotations::PmiLineWeld*>(
//                                                               stdVecector[0]))
//                                                        continue;
//                                                }
//                                                // ---------------------------------------------------------
//                                                if ((*it) && ((*it)->Tag() != NULL_TAG)
//                                                    && !list.contains((*it)->Tag())
//                                                    && (!isPmiToUse
//                                                        || UGPmiRootClass((*it)->Tag())
//                                                               .isPmiToUse(manualSelection)))
//                                                    list << (*it)->Tag();
//                                            }
//                                        }
//                                    } else {
//                                        Q_DEBUG("The component part pmi manager is invalid.");
//                                    }
//                                } else {
//                                    Q_DEBUG("The component part is invalid.");
//                                }
//                            } else {
//                                Q_WARNING("The given object is not a Component.");
//                            }
//                        } else {
//                            Q_WARNING("The given object is not a TaggedObject.");
//                        }
//
//                        /* ------------------------------------------------------ */
//
//                    } else {
//                        Q_DEBUG("The current pmi manager is invalid.");
//                    }
//                } else {
//                    Q_DEBUG("The current work part is invalid.");
//                }
//            } else {
//                Q_DEBUG("The current session is invalid.");
//            }
//        }
//        NXOPEN_CATCH
//    }
//    return list;
//}


/**
 *
 */
//QString UGPmiRootClass::debugInfo(void) const
//{
//    UG_INITIALIZE;
//    Q_DEBUG_SCOPE log;
//    QString debug(QString(" BCT DEBUG INFO (%2):").arg(_PmiObjectCounter));
//
//    NXOPEN_TRY
//    {
//        if (_pmi) {
//
//            debug += QString(" - PMI (%1 / %2)")
//                         .arg(Qt4UgOpen::UGObject(_pmi->Tag()).type())
//                         .arg(Qt4UgOpen::UGObject(_pmi->Tag()).subtype());
//
//            std::vector<NXOpen::Annotations::Annotation*> stdVec = _pmi->GetDisplayInstances();
//            if (!stdVec.empty() && stdVec[0])
//                debug += QString(" - INSTANCE (%1 / %2)")
//                             .arg(Qt4UgOpen::UGObject(stdVec[0]->Tag()).type())
//                             .arg(Qt4UgOpen::UGObject(stdVec[0]->Tag()).subtype());
//        }
//    }
//    NXOPEN_CATCH
//
//    return debug;
//}


/**
 *
 */
bool UGPmiRootClass::setLayer(const int& layer, const int& layer_status /* = 0*/) const {
    bool ret_ok = false;

    if (_session && _work_part && _owning_part && _pmi_manager && _pmi) {
        /* ------------------------------------------------------ */

        std::vector<NXOpen::Annotations::Annotation*> std_vec = _pmi->GetDisplayInstances();
        if (!std_vec.empty()                               //
            && layer >= NXOpen::Layer::ConstantsFirstLayer //
            && layer <= NXOpen::Layer::ConstantsLastLayer) {
            // set layer state ...
            if (layer_status > 0 && layer_status < 3) {
                NXOpen::Layer::State state // map our staus to the NX status
                        = (layer_status == 1 ? NXOpen::Layer::StateVisible : NXOpen::Layer::StateSelectable);
                if (NXOpen::Layer::LayerManager* layer_manager = _work_part->Layers()) {
                    for (int i = 0; i < (int) std_vec.size(); i++) {
                        std::vector<NXOpen::View*> views = std_vec[i]->GetViews();
                        for (int j = 0; j < (int) views.size(); j++) {
                            if (NXOpen::View* view = views[j]) {
                                std::vector<NXOpen::Layer::StateInfo> state_array(
                                        NXOpen::Layer::ConstantsLastLayer);
                                layer_manager->GetVisibilitiesInView(view, state_array);
                                if (state_array[layer - 1].State != state) {
                                    state_array[layer - 1] = NXOpen::Layer::StateInfo(layer, state);
                                    layer_manager->SetObjectsVisibilityOnLayer(view, state_array, true);
                                }
                            }
                        }
                    }
                }
            }

            // set layer ...
            std::vector<NXOpen::DisplayableObject*> objects(std_vec.size());
            for (int i = 0; i < (int) std_vec.size(); i++)
                objects[i] = std_vec[i];

            _work_part->Layers()->MoveDisplayableObjects(layer, objects);

            ret_ok = true; // ...do it at the end, than we sure there will be no exception anymore...
        }
        else {
            ret_ok = (layer == 0); // it is already work layer by default
        }

        /* ------------------------------------------------------ */
    }

    return ret_ok;
}


/**
 *
 */
std::string UGPmiRootClass::name(void) const
{
    return "1";
    //std::string pmiName;

    //    if (_session && _work_part && _owning_part && _pmi_manager && _pmi) {

    //        /* ------------------------------------------------------ */

    //        pmiName = QSTRING_NX(_pmi->Name());

    //        /* ------------------------------------------------------ */

    //    } else {
    //        Q_DEBUG("The object is not fully initialized.");
    //    }

    //return pmiName;
}


/**
 *
 */
bool UGPmiRootClass::setName(const std::string& name, const bool& all /* = true*/) const {
    return true;
    //bool ret_ok = false;


    //if (_session && _work_part && _owning_part && _pmi_manager && _pmi) {
    //    /* ------------------------------------------------------ */

    //    if (!name.trimmed().isEmpty()) {
    //        ret_ok = true;
    //        _pmi->SetName("hello" /*NXSTRING_Q(name.trimmed())*/);

    //        if (all) {
    //            std::vector<NXOpen::Annotations::Annotation*> stdVec = _pmi->GetDisplayInstances();
    //            for (int i = 0; i < (int) stdVec.size(); i++)
    //                stdVec[i]->SetName(NXSTRING_Q(name.trimmed()));
    //        }
    //    }
    //    else {
    //        Q_WARNING("Is not allowed to set an empty name.");
    //    }

    //    /* ------------------------------------------------------ */
    //}
    //else {
    //    Q_DEBUG("The object is not fully initialized.");
    //}
    //return ret_ok;
}


/**
 *
 */
bool UGPmiRootClass::references(QList<BctInspector::CADInterface::sREFERENCE>* references /* = 0*/) const
{
    bool hasReferences = false;

    NXOPEN_TRY
    {
        if (_session && _work_part && _owning_part && _pmi_manager && _pmi) {

            /* ------------------------------------------------------ */

            if (NXOpen::Annotations::AssociatedObject* associatedObject = _pmi->GetAssociatedObject()) {

#if (UG_VERSION >= 290)
                std::vector<NXOpen::NXObject*> stdVec = associatedObject->GetObjects();
                for (int i = 0; (references && i < (int)stdVec.size()); i++) {
                    if (NXOpen::NXObject* obj = stdVec[i]) {
#else
                std::vector<NXOpen::DisplayableObject*> stdVec = associatedObject->GetObjects();
                for (int i = 0; (references && i < (int)stdVec.size()); i++) {
                    if (NXOpen::DisplayableObject* obj = stdVec[i]) {
#endif
                        NXOpen::Point3d vertex1(0.0, 0.0, 0.0);
                        NXOpen::Point3d vertex2(0.0, 0.0, 0.0);
                        if (NXOpen::Face* face = dynamic_cast<NXOpen::Face*>(obj)) {
                            std::vector<NXOpen::Edge*> edges = face->GetEdges();
                            if (!edges.empty() && edges[0])
                                edges[0]->GetVertices(&vertex1, &vertex2);
                        } else if (NXOpen::Edge* edge = dynamic_cast<NXOpen::Edge*>(obj)) {
                            edge->GetVertices(&vertex1, &vertex2);
                        }
                        double origin[] = {vertex1.X, vertex1.Y, vertex1.Z};
                        references->append(
                            BctInspector::CADInterface::sREFERENCE(UgObjectId(obj->Tag()).uid(), origin));
                    }
                }
                hasReferences = !stdVec.empty();

            } else {
                Q_WARNING("No associated objects found.");
            }

            /* ------------------------------------------------------ */

        } else {
            Q_DEBUG("The object is not fully initialized.");
        }
    }
    NXOPEN_CATCH

    return hasReferences;
}


/**
 * Attributes: Key: TITLE - Value: value.first  => title (cad), value.second => value (cad)
 */
QString UGPmiRootClass::attributes(const QString& name) const
{
    UG_INITIALIZE;
    Q_DEBUG_SCOPE log;
    QString value;

    NXOPEN_TRY
    {
        if (_session && _work_part && _owning_part && _pmi_manager && _pmi) {

            /* ------------------------------------------------------ */

            QMap<QString, QPair<QString, QString> > a(attributes());
            QMap<QString, QPair<QString, QString> > attributes(a); // FIXME: gcc doesn't like "X Y(Y());"
            if (attributes.contains(name.toUpper()))
                value = attributes[name.toUpper()].second;

            /* ------------------------------------------------------ */

        } else {
            Q_DEBUG("The object is not fully initialized.");
        }
    }
    NXOPEN_CATCH

    return value;
}


/**
 * Attributes: Key: TITLE - Value: value.first  => title (cad), value.second => value (cad)
 */
QMap<QString, QPair<QString, QString> > UGPmiRootClass::attributes(void) const
{
    UG_INITIALIZE;
    Q_DEBUG_SCOPE log;
    QMap<QString, QPair<QString, QString> > attributes;

    NXOPEN_TRY
    {
        if (_session && _work_part && _owning_part && _pmi_manager && _pmi) {

            /* ------------------------------------------------------ */

            QList<NXOpen::Annotations::Annotation*> dispInst(displayInstances());
            if (!dispInst.isEmpty() && dispInst[0]) {
                std::vector<NXOpen::NXObject::AttributeInformation> stdVec = dispInst[0]->GetUserAttributes();
                foreach (NXOpen::NXObject::AttributeInformation attribute,
                         QVector<NXOpen::NXObject::AttributeInformation>::fromStdVector(stdVec)) {
                    // ---------------------------------------------------------
                    // ER for ATK (See/Grep for Mantis: #0006096)
                    // ---------------------------------------------------------
                    switch (attribute.Type) {
                    case NXOpen::NXObject::AttributeTypeInvalid:
                        break;
                    case NXOpen::NXObject::AttributeTypeNull:
                        attributes[QSTRING_NX(attribute.Title).toUpper()]
                            = qMakePair(QSTRING_NX(attribute.Title), QString());
                        break;
                    case NXOpen::NXObject::AttributeTypeBoolean:
                        attributes[QSTRING_NX(attribute.Title).toUpper()] = qMakePair(
                            QSTRING_NX(attribute.Title), QString(attribute.BooleanValue ? "True" : "False"));
                        break;
                    case NXOpen::NXObject::AttributeTypeInteger:
                        attributes[QSTRING_NX(attribute.Title).toUpper()]
                            = qMakePair(QSTRING_NX(attribute.Title), QString::number(attribute.IntegerValue));
                        break;
                    case NXOpen::NXObject::AttributeTypeReal:
                        attributes[QSTRING_NX(attribute.Title).toUpper()]
                            = qMakePair(QSTRING_NX(attribute.Title), QString::number(attribute.RealValue));
                        break;
                    case NXOpen::NXObject::AttributeTypeString:
                        attributes[QSTRING_NX(attribute.Title).toUpper()]
                            = qMakePair(QSTRING_NX(attribute.Title), QSTRING_NX(attribute.StringValue));
                        break;
                    case NXOpen::NXObject::AttributeTypeTime:
                        attributes[QSTRING_NX(attribute.Title).toUpper()]
                            = qMakePair(QSTRING_NX(attribute.Title), QSTRING_NX(attribute.TimeValue));
                        break;
                    // DEPRECATED -> case NXOpen::NXObject::AttributeTypeReference:
                    // DEPRECATED ->     attributes[QSTRING_NX(attribute.Title).toUpper()]
                    // DEPRECATED ->         = qMakePair(QSTRING_NX(attribute.Title),
                    // DEPRECATED ->             QSTRING_NX(attribute.ReferenceValue));
                    // DEPRECATED ->     break;
                    default:
                        Q_DEBUG("Attribute " << QSTRING_NX(attribute.Title) << " uses an unsupported type: "
                                             << attribute.Type);
                        break;
                    }
                    // ---
                    // ---------------------------------------------------------
                }
            } else {
                Q_DEBUG("The object has no instances.");
            }

            /* ------------------------------------------------------ */

        } else {
            Q_DEBUG("The object is not fully initialized.");
        }
    }
    NXOPEN_CATCH

    return attributes;
}


/**
 *
 */
int UGPmiRootClass::index(void) const
{
    UG_INITIALIZE;
    Q_DEBUG_SCOPE log;

    int pmiIndex;

    NXOPEN_TRY
    {
        if (_session && _work_part && _owning_part && _pmi_manager && _pmi) {

            /* ------------------------------------------------------ */

            pmiIndex = _pmi->Index();

            /* ------------------------------------------------------ */

        } else {
            Q_DEBUG("The object is not fully initialized.");
        }
    }
    NXOPEN_CATCH

    return pmiIndex;
}


/**
 * Calculates the corresponding scale to the text
 *
 * @returns the corresponding scale
 */
/*static*/ double UGPmiRootClass::calcPmiBalloonScale(QString text, ///< the balloon text
                                                      double size   ///< used character size (metric system)
                                                      )
{
    // Stop guessing around to find a smart theorem to calculate
    // the scale. Now we are using the defaults which are used
    // inside the dialog during the manual creation of balloons
    // inside NX.
    const double default_chars = 1.0;
    const double default_scale = 1.0;
    const double default_size = 3.5;

    /* ------------------------------------------------------ */

    Q_DEBUG_SCOPE log;
    double scale = 1.0;

    /* ------------------------------------------------------ */

    // removes all control characters, these should not be
    // taken to account
    // the same like "CHXStamps::noControlChars(...)"
    const QRegExp regexp("^\\s*<.+>(.+)<.+>\\s*$");
    while (regexp.indexIn(text) > (-1))
        text = regexp.cap(1);

    /* ------------------------------------------------------ */

    // transform the size if needed
    //QString unit;
    //if (cad()->getDisplayPartUnit(&unit) && unit == Unit::inch())
    //    size *= 25.4;

    /* ------------------------------------------------------ */

    // estimate scale based on proportion scale per character
    const double scale_char = default_scale / default_chars * double(text.length());

    /* ------------------------------------------------------ */

    // estimate scale based on proportion scale per size
    const double scale_size = default_scale / default_size * size;

    /* ------------------------------------------------------ */

    // estimate scale based on the length
    scale = scale_char * scale_size;

    /* ------------------------------------------------------ */

    // now we try again to be smarter than NX and reduce the
    // size to ...
    switch (text.length()) {
    case 1:
        scale *= 0.5; // %
        break;
    default:
        scale *= 0.4; // %
        break;
    }

    return scale > 0.0 ? scale : 1.0;
}


/**
 * Set the best fitting association data for an object which should be associated to this (e.g. BalloonNote)
 *
 * @param assoc_origin [out] Data structure is going to be edited on success
 *
 * @returns true on success
 */
bool UGPmiRootClass::preferedOriginData(
    NXOpen::Annotations::Annotation::AssociativeOriginData* assoc_origin) const
{
    UG_INITIALIZE;
    Q_DEBUG_SCOPE log;
    bool ret_ok = false;

    NXOPEN_TRY
    {
        if (_session && _work_part && _owning_part && _pmi_manager && _pmi) {

            /* ------------------------------------------------------ */

            if (assoc_origin) {

                assoc_origin->OriginType = NXOpen::Annotations::AssociativeOriginTypeAttachedToStack;
                assoc_origin->PointOnGeometry = NULL;
                assoc_origin->VertAnnotation = NULL;
                assoc_origin->VertAlignmentPosition = NXOpen::Annotations::AlignmentPositionTopLeft;
                assoc_origin->HorizAnnotation = NULL;
                assoc_origin->HorizAlignmentPosition = NXOpen::Annotations::AlignmentPositionTopLeft;
                assoc_origin->AlignedAnnotation = NULL;
                assoc_origin->DimensionLine = 0;
                assoc_origin->AssociatedPoint = NULL;
                assoc_origin->OffsetAnnotation = NULL;
                assoc_origin->OffsetAlignmentPosition = NXOpen::Annotations::AlignmentPositionTopLeft;
                assoc_origin->XOffsetFactor = 0.0;
                assoc_origin->YOffsetFactor = 0.0;
                assoc_origin->StackAlignmentPosition = NXOpen::Annotations::StackAlignmentPositionRight;

                // general check to see if we have something possibly with a leader...
                if (NXOpen::Annotations::Annotation* anno = dynamic_cast<NXOpen::Annotations::Annotation*>(displayInstances().value(0))) {

                    // if it's a dimension look a bit closer...
                    if (NXOpen::Annotations::Dimension* dim = dynamic_cast<NXOpen::Annotations::Dimension*>(anno)) {
                        if (!dim->IsOriginCentered()) {
                            if (anno->LeaderOrientation() == NXOpen::Annotations::LeaderOrientationFromRight)
                                assoc_origin->StackAlignmentPosition
                                    = NXOpen::Annotations::StackAlignmentPositionLeft;

                        }
                        else if (NXOpen::Annotations::DimensionPreferences* dim_pref = dim->GetDimensionPreferences()) {}
                    } else if (anno->LeaderOrientation() == NXOpen::Annotations::LeaderOrientationFromRight) {
                        assoc_origin->StackAlignmentPosition = NXOpen::Annotations::StackAlignmentPositionLeft;
                    }
                }

                ret_ok = true; // ...do it at the end, than we sure there will be no exception anymore...
            }

            /* ------------------------------------------------------ */

        } else {
            log << "The object is not fully initialized.";
        }
    }
    NXOPEN_CATCH

    return ret_ok;
}


/**
 *
 */
bool UGPmiRootClass::angle(double* angle) const
{
    UG_INITIALIZE;
    Q_DEBUG_SCOPE log;
    bool ret_ok = false;

    NXOPEN_TRY
    {
        if (_session && _work_part && _owning_part && _pmi_manager && _pmi) {

            /* ------------------------------------------------------ */

            if (angle) {
                std::vector<NXOpen::Annotations::Annotation*> stdVec = _pmi->GetDisplayInstances();
                if (!stdVec.empty()) {
                    if (NXOpen::Annotations::LetteringPreferences* preferences
                        = stdVec[0]->GetLetteringPreferences()) {
                        ret_ok = true;
                        (*angle) = preferences->Angle();
                        delete (preferences);
                    }
                }
            }

            /* ------------------------------------------------------ */

        } else {
            Q_DEBUG("The object is not fully initialized.");
        }
    }
    NXOPEN_CATCH

    return ret_ok;
}


/**
 *
 */
bool UGPmiRootClass::setAngle(const double& angle) const
{
    UG_INITIALIZE;
    Q_DEBUG_SCOPE log;
    bool ret_ok = false;

    NXOPEN_TRY
    {
        if (_session && _work_part && _owning_part && _pmi_manager && _pmi) {

            /* ------------------------------------------------------ */

            std::vector<NXOpen::Annotations::Annotation*> stdVec = _pmi->GetDisplayInstances();
            for (int i = 0; i < (int)stdVec.size(); i++) {
                if (NXOpen::Annotations::LetteringPreferences* preferences
                    = stdVec[i]->GetLetteringPreferences()) {
                    ret_ok = true;
                    preferences->SetAngle(angle);
                    stdVec[i]->SetLetteringPreferences(preferences);
                    delete (preferences);
                }
            }

            /* ------------------------------------------------------ */

        } else {
            Q_DEBUG("The object is not fully initialized.");
        }
    }
    NXOPEN_CATCH

    return ret_ok;
}


/**
 *
 */
bool UGPmiRootClass::symbolPreferences(NXOpen::Annotations::LineCfw* userCfw) const
{
    UG_INITIALIZE;
    Q_DEBUG_SCOPE log;
    bool ret_ok = false;

    NXOPEN_TRY
    {
        if (_session && _work_part && _owning_part && _pmi_manager && _pmi) {

            /* ------------------------------------------------------ */

            if (userCfw) {
                std::vector<NXOpen::Annotations::Annotation*> stdVec = _pmi->GetDisplayInstances();
                if (!stdVec.empty()) {
                    if (NXOpen::Annotations::SymbolPreferences* preferences
                        = stdVec[0]->GetSymbolPreferences()) {
                        ret_ok = true;
                        (*userCfw) = preferences->GetUserDefinedSymbolCfw();
                        delete (preferences);
                    }
                }
            }

            /* ------------------------------------------------------ */

        } else {
            Q_DEBUG("The object is not fully initialized.");
        }
    }
    NXOPEN_CATCH

    return ret_ok;
}


/**
 *
 */
bool UGPmiRootClass::setSymbolPreferences(const NXOpen::Annotations::LineCfw& userCfw) const
{
    UG_INITIALIZE;
    Q_DEBUG_SCOPE log;
    bool ret_ok = false;

    NXOPEN_TRY
    {
        if (_session && _work_part && _owning_part && _pmi_manager && _pmi) {

            /* ------------------------------------------------------ */

            std::vector<NXOpen::Annotations::Annotation*> stdVec = _pmi->GetDisplayInstances();
            for (int i = 0; i < (int)stdVec.size(); i++) {
                if (NXOpen::Annotations::SymbolPreferences* preferences = stdVec[i]->GetSymbolPreferences()) {
                    ret_ok = true;
                    preferences->SetUserDefinedSymbolCfw(userCfw);
                    stdVec[i]->SetSymbolPreferences(preferences);
                    delete (preferences);
                }
            }

            /* ------------------------------------------------------ */

        } else {
            Q_DEBUG("The object is not fully initialized.");
        }
    }
    NXOPEN_CATCH

    return ret_ok;
}


/**
 *
 */
bool UGPmiRootClass::textPreferences(NXOpen::Annotations::Lettering* general) const
{
    UG_INITIALIZE;
    Q_DEBUG_SCOPE log;
    bool ret_ok = false;

    NXOPEN_TRY
    {
        if (_session && _work_part && _owning_part && _pmi_manager && _pmi) {

            /* ------------------------------------------------------ */

            if (general) {
                std::vector<NXOpen::Annotations::Annotation*> stdVec = _pmi->GetDisplayInstances();
                if (!stdVec.empty()) {
                    if (NXOpen::Annotations::LetteringPreferences* preferences
                        = stdVec[0]->GetLetteringPreferences()) {

                        ret_ok = true;

                        // NXOpen::Annotations::Lettering myAppended  = preferences->GetAppendedText();
                        NXOpen::Annotations::Lettering myDimension = preferences->GetDimensionText();
                        NXOpen::Annotations::Lettering myGeneral = preferences->GetGeneralText();
                        // NXOpen::Annotations::Lettering myTolerance = preferences->GetToleranceText();
                        if (dynamic_cast<NXOpen::Annotations::Dimension*>(stdVec[0]))
                            (*general) = myDimension;
                        else
                            (*general) = myGeneral;

                        delete (preferences);
                    }
                }
            }

            /* ------------------------------------------------------ */

        } else {
            Q_DEBUG("The object is not fully initialized.");
        }
    }
    NXOPEN_CATCH

    return ret_ok;
}


/**
 *
 */
bool UGPmiRootClass::setTextPreferences(const NXOpen::Annotations::Lettering& general) const
{
    UG_INITIALIZE;
    Q_DEBUG_SCOPE log;
    bool ret_ok = false;

    NXOPEN_TRY
    {
        if (_session && _work_part && _owning_part && _pmi_manager && _pmi) {

            /* ------------------------------------------------------ */

            std::vector<NXOpen::Annotations::Annotation*> stdVec = _pmi->GetDisplayInstances();
            for (int i = 0; i < (int)stdVec.size(); i++) {
                if (NXOpen::Annotations::LetteringPreferences* preferences
                    = stdVec[i]->GetLetteringPreferences()) {
                    ret_ok = true;
                    preferences->SetGeneralText(general);
                    stdVec[i]->SetLetteringPreferences(preferences);
                    delete (preferences);
                }
            }

            /* ------------------------------------------------------ */

        } else {
            Q_DEBUG("The object is not fully initialized.");
        }
    }
    NXOPEN_CATCH

    return ret_ok;
}


/**
 *
 */
bool UGPmiRootClass::setHighlight(const bool& on) const
{
    UG_INITIALIZE;
    Q_DEBUG_SCOPE log;
    bool ret_ok = false;

    NXOPEN_TRY
    {
        if (_session && _work_part && _owning_part && _pmi_manager && _pmi) {

            /* ------------------------------------------------------ */

            ret_ok = true;
            std::vector<NXOpen::Annotations::Annotation*> stdVec = _pmi->GetDisplayInstances();
            for (int i = 0; i < (int)stdVec.size(); i++) {
                if (on)
                    stdVec[i]->Highlight();
                else
                    stdVec[i]->Unhighlight();
            }

            /* ------------------------------------------------------ */

        } else {
            Q_DEBUG("The object is not fully initialized.");
        }
    }
    NXOPEN_CATCH

    return ret_ok;
}


/**
 *
 */
bool UGPmiRootClass::setVisible(const bool& visible) const
{
    UG_INITIALIZE;
    Q_DEBUG_SCOPE log;
    bool ret_ok = false;

    NXOPEN_TRY
    {
        if (_session && _work_part && _owning_part && _pmi_manager && _pmi) {

            /* ------------------------------------------------------ */

            std::vector<NXOpen::Annotations::Annotation*> stdVec = _pmi->GetDisplayInstances();
            if (!stdVec.empty()) {
                ret_ok = true;

                std::vector<NXOpen::DisplayableObject*> objects(stdVec.size());
                for (int i = 0; i < (int)stdVec.size(); i++)
                    objects[i] = stdVec[i];

                if (visible) {
                    _session->DisplayManager()->UnblankObjects(objects);
                    // Replace it by NX Open C++ ...
                    for (int i = 0; i < (int)objects.size(); i++)
                        UG_OK(UF_DISP_add_item_to_display(objects[i]->Tag()));
                    // ...
                } else {
                    _session->DisplayManager()->BlankObjects(objects);
                }
            }

            /* ------------------------------------------------------ */

        } else {
            Q_DEBUG("The object is not fully initialized.");
        }
    }
    NXOPEN_CATCH

    return ret_ok;
}


/**
 *
 */
bool UGPmiRootClass::remove(void)
{
    UG_INITIALIZE;
    Q_DEBUG_SCOPE log;
    bool ret_ok = false;

    NXOPEN_TRY
    {
        if (_session && _work_part && _owning_part && _pmi_manager && _pmi) {

            /* ------------------------------------------------------ */

            NXOpen::Session::UndoMarkId markId
                = _session->SetUndoMark(NXOpen::Session::MarkVisibilityInvisible, "Delete");
            std::vector<NXOpen::NXObject*> objects(1);
            objects[0] = _pmi;
            int error = _session->UpdateManager()->AddToDeleteList(objects);
            if (!error) {
                error = _session->UpdateManager()->DoUpdate(markId);
                _session->DeleteUndoMark(markId, "Delete");
                if (!error) {
                    ret_ok = true;
                    _session = 0;
                    _work_part = 0;
                    _owning_part = 0;
                    _pmi_manager = 0;
                    _pmi = 0;
                    _part_tag = NULL_TAG;
                }
            }
            if (error) {
                Q_WARNING("Method failed with error: " << error);
            }

            /* ------------------------------------------------------ */

        } else {
            Q_DEBUG("The object is not fully initialized.");
        }
    }
    NXOPEN_CATCH

    return ret_ok;
}


/**
 * @warning: The "QList<NXOpen::View*> views(void) const" method is just a Prototype!
 *
 *           Currently this method is only used to get the right view of a component
 *           PMI inside the Root-/Parent-Part.  We need this to place the Balloons!
 */
QList<NXOpen::View*> UGPmiRootClass::views(void) const
{
    UG_INITIALIZE;
    Q_DEBUG_SCOPE log;
    QList<NXOpen::View*> list;

    NXOPEN_TRY
    {
        if (_session && _work_part && _owning_part && _pmi_manager && _pmi) {

            /* ------------------------------------------------------ */

            if (_work_part != _owning_part) {
                if (NXOpen::Annotations::PmiFilterCollection* pfc = _work_part->Annotations()->PmiFilters()) {
                    for (NXOpen::Annotations::PmiFilterCollection::iterator it = pfc->begin();
                         it != pfc->end();
                         it++) {
                        if (NXOpen::Annotations::PmiFilterByView* pfbv
                            = dynamic_cast<NXOpen::Annotations::PmiFilterByView*>(*it)) {

                            // std::vector<NXOpen::NXString> views = pfbv->GetPmiViews();
                            // for (int v = 0; v < (int)views.size(); v++)
                            //     Q_WARNING("Original view name of the PMI: " << QSTRING_NX(views[v]));

                            std::vector<NXOpen::View*> views = pfbv->GetAppliedViews();
                            for (int v = 0; v < (int)views.size(); v++) {
                                if (views[v] && !list.contains(views[v])) {
                                    // Q_WARNING(QSTRING_NX(views[v]->Name()));
                                    list << views[v];
                                }
                            }
                        }
                    }
                }

            } else {

                foreach (NXOpen::Annotations::Annotation* a, displayInstances()) {
                    if (a) {
                        std::vector<NXOpen::View*> views = a->GetViews();
                        for (int v = 0; v < (int)views.size(); v++) {
                            if (views[v] && !list.contains(views[v]))
                                list << views[v];
                        }
                    }
                }
            }

            /* ------------------------------------------------------ */

        } else {
            Q_DEBUG("The object is not fully initialized.");
        }
    }
    NXOPEN_CATCH

    return list;
}


/**
 * Get modeling view of the specified name
 *
 * @returns the modeling view of the #name, if the view doesn't exist NULL will be returned
 */
/* static */ NXOpen::ModelingView*
UGPmiRootClass::modelingViewOfName(const QString& name ///< name of the view
                                   )
{
    Q_DEBUG_SCOPE log;
    NXOpen::ModelingView* view(NULL);

    NXOPEN_TRY
    {
        if (NXOpen::Session* session = NXOpen::Session::GetSession()) {
            if (NXOpen::Part* work_part = session->Parts()->Work()) {

                /* ------------------------------------------------------ */

                if (NXOpen::ModelingViewCollection* modeling_view_collection = work_part->ModelingViews()) {
                    if (!name.isEmpty()) {

                        for (NXOpen::ModelingViewCollection::iterator it = modeling_view_collection->begin();
                             it != modeling_view_collection->end();
                             it++) {
                            if (*it && QSTRING_NX((*it)->Name()).toUpper() == name.toUpper()) {
                                view = *it;
                                break;
                            }
                        }

                        if (!view)
                            log << "Can not find a modeling view with the name: " << name;

                    } else {
                        log.warning("Can not find a modeling view without a name.");
                    }
                } else {
                    log.warning("The modeling view collection is invalid.");
                }

                /* ------------------------------------------------------ */

            } else {
                log << "The current work part is invalid.";
            }
        } else {
            log << "The current session is invalid.";
        }
    }
    NXOPEN_CATCH

    return view;
}


/**
 *
 */
QList<NXOpen::Annotations::Annotation*> UGPmiRootClass::displayInstances(void) const
{
    UG_INITIALIZE;
    Q_DEBUG_SCOPE log;
    QList<NXOpen::Annotations::Annotation*> list;

    NXOPEN_TRY
    {
        if (_session && _work_part && _owning_part && _pmi_manager && _pmi) {

            /* ------------------------------------------------------ */

            std::vector<NXOpen::Annotations::Annotation*> stdVec = _pmi->GetDisplayInstances();
            if (!stdVec.empty()) {
                QVector<NXOpen::Annotations::Annotation*> vector
                    = QVector<NXOpen::Annotations::Annotation*>::fromStdVector(stdVec);
                if (!vector.isEmpty())
                    list = vector.toList();
            }

            /* ------------------------------------------------------ */

        } else {
            Q_DEBUG("The object is not fully initialized.");
        }
    }
    NXOPEN_CATCH

    return list;
}


/**
 *
 */
bool UGPmiRootClass::getLocationInfo(CADDataLine* data_line) const
{
    UG_INITIALIZE;
    Q_DEBUG_SCOPE log;
    bool ret_ok = false;

    NXOPEN_TRY
    {
        if (_session && _work_part && _owning_part && _pmi_manager && _pmi) {

            /* ------------------------------------------------------ */

            if (data_line) {
                data_line->setErrorOf(KEY_2505, CnItem::NoError);
                data_line->setObjPositionError('X', CnItem::NoError);
                data_line->setObjPositionError('Y', CnItem::NoError);
                data_line->setObjPositionError('Z', CnItem::NoError);
                QList<NXOpen::Annotations::Annotation*> disp_inst(displayInstances());
                if (!disp_inst.isEmpty() && disp_inst[0]) {
                    ret_ok = true;

                    std::vector<NXOpen::View*> view = disp_inst[0]->GetViews();
                    if (!view.empty() && view[0]) {
                        data_line->setValueOf(KEY_2505, QSTRING_NX(view[0]->Name()));

                    } else {
                        Q_DEBUG("Determination of the view failed.");
                        data_line->setErrorOf(KEY_2505, CnItem::AskInfoError);
                        // ---------------------------------------------------------
                        // PR 1901310: If PMI is applied to all views, by selecting
                        // "in all Views" the API doesn't return the views!
                        // ---------------------------------------------------------
                        Q_WARNING("Determination of the view failed."
                                  << " - WORKAROUND: If the failed PMI was applied to all"
                                  << " views by using the menu entry \"in all Views\","
                                  << " the problem could be solved by manually adding"
                                  << " the PMI to the different views.");
                        // ---------------------------------------------------------
                    }

                    const double unit_factor
                        = Qt4UgOpen::UGPart(disp_inst[0]->Tag()).isEnglish() ? 25.4 : 1.0;
                    NXOpen::Point3d point_3d = disp_inst[0]->AnnotationOrigin();
                    data_line->setObjPosition('X', CADDataItem(qRound(point_3d.X * unit_factor)));
                    data_line->setObjPosition('Y', CADDataItem(qRound(point_3d.Y * unit_factor)));
                    data_line->setObjPosition('Z', CADDataItem(qRound(point_3d.Z * unit_factor)));

                    data_line->setInternalPosition('X', CADDataItem(point_3d.X));
                    data_line->setInternalPosition('Y', CADDataItem(point_3d.Y));
                    data_line->setInternalPosition('Z', CADDataItem(point_3d.Z));
                } else {
                    Q_DEBUG("The object has no instances.");
                }
            } else {
                Q_DEBUG("The line passed in is invalid.");
            }

            /* ------------------------------------------------------ */

        } else {
            Q_DEBUG("The object is not fully initialized.");
        }
    }
    NXOPEN_CATCH

    return ret_ok;
}


/**
 *
 */
bool UGPmiRootClass::createDataTable(CADDataTable* dataTable, const bool& manualSelection /* = false*/) const
{
    UG_INITIALIZE;
    Q_DEBUG_SCOPE log;
    bool ret_ok = false;

    NXOPEN_TRY
    {
        QString part_uid(UgObjectId::getUniqueHandleOfPartTag(_part_tag));
        UgObjectId ugObjId(tag());
        if (_session && _work_part && _owning_part && _pmi_manager && _pmi && _part_tag != NULL_TAG
            && !part_uid.isEmpty()
            && ugObjId.tag() != NULL_TAG
            && !ugObjId.uid().isEmpty()) {

            /* ------------------------------------------------------ */

            if (dataTable) {
                if (!dataTable->isEmpty())
                    dataTable->clear();
                QList<NXOpen::Annotations::Annotation*> dispInst(displayInstances());
                if (!dispInst.isEmpty() && dispInst[0]) {
                    if (NXOpen::Annotations::Dimension* dimension
                        = dynamic_cast<NXOpen::Annotations::Dimension*>(dispInst[0])) {
                        ret_ok
                            = dataDimension(dataTable, part_uid, ugObjId.uid(), dimension, manualSelection);

                    } else if (NXOpen::Annotations::Fcf* fcf
                               = dynamic_cast<NXOpen::Annotations::Fcf*>(dispInst[0])) {
                        ret_ok = dataFcf(dataTable, part_uid, ugObjId.uid(), fcf, manualSelection);

                    } else if (NXOpen::Annotations::Datum* datum
                               = dynamic_cast<NXOpen::Annotations::Datum*>(dispInst[0])) {
                        ret_ok = dataDatum(dataTable, part_uid, ugObjId.uid(), datum, manualSelection);

                    } else if (NXOpen::Annotations::PmiCustomSymbol* customSymbol
                               = dynamic_cast<NXOpen::Annotations::PmiCustomSymbol*>(dispInst[0])) {
                        ret_ok = dataCustomSymbol(dataTable, part_uid, ugObjId.uid(), customSymbol);

                        // Deprecated in NX6.0.0 ->
                    } else if (NXOpen::Annotations::PmiLabel* label
                               = dynamic_cast<NXOpen::Annotations::PmiLabel*>(dispInst[0])) {
                        ret_ok = dataLabelNote(
                            dataTable,
                            part_uid,
                            ugObjId.uid(),
                            dynamic_cast<NXOpen::Annotations::SimpleDraftingAid*>(dispInst[0]),
                            manualSelection);
                        // <- Deprecated in NX6.0.0

                    } else if (NXOpen::Annotations::PmiNote* note
                               = dynamic_cast<NXOpen::Annotations::PmiNote*>(dispInst[0])) {
                        ret_ok = dataLabelNote(
                            dataTable,
                            part_uid,
                            ugObjId.uid(),
                            dynamic_cast<NXOpen::Annotations::SimpleDraftingAid*>(dispInst[0]),
                            manualSelection);

                    } else if (NXOpen::Annotations::BalloonNote* attribute
                               = dynamic_cast<NXOpen::Annotations::BalloonNote*>(dispInst[0])) {
                        ret_ok = dataBalloonNote(dataTable, part_uid, ugObjId.uid(), attribute);

                    } else if (NXOpen::Annotations::CompanyProprietaryInformation* attribute
                               = dynamic_cast<NXOpen::Annotations::CompanyProprietaryInformation*>(
                                   dispInst[0])) {
                        ret_ok = fallbackBaseIntegration(
                            dataTable, part_uid, ugObjId.uid(), "CompanyProprietaryInformation");

                    } else if (NXOpen::Annotations::CoordinateNote* attribute
                               = dynamic_cast<NXOpen::Annotations::CoordinateNote*>(dispInst[0])) {
                        ret_ok
                            = fallbackBaseIntegration(dataTable, part_uid, ugObjId.uid(), "CoordinateNote");

                    } else if (NXOpen::Annotations::EnterpriseIdentification* attribute
                               = dynamic_cast<NXOpen::Annotations::EnterpriseIdentification*>(dispInst[0])) {
                        ret_ok = fallbackBaseIntegration(
                            dataTable, part_uid, ugObjId.uid(), "EnterpriseIdentification");

                    } else if (NXOpen::Annotations::ExportControl* attribute
                               = dynamic_cast<NXOpen::Annotations::ExportControl*>(dispInst[0])) {
                        ret_ok = fallbackBaseIntegration(dataTable, part_uid, ugObjId.uid(), "ExportControl");

                    } else if (NXOpen::Annotations::GeneralNote* attribute
                               = dynamic_cast<NXOpen::Annotations::GeneralNote*>(dispInst[0])) {
                        ret_ok
                            = dataGeneralNote(dataTable, part_uid, ugObjId.uid(), attribute, manualSelection);

                    } else if (NXOpen::Annotations::GovernmentSecurityInformation* attribute
                               = dynamic_cast<NXOpen::Annotations::GovernmentSecurityInformation*>(
                                   dispInst[0])) {
                        ret_ok = fallbackBaseIntegration(
                            dataTable, part_uid, ugObjId.uid(), "GovernmentSecurityInformation");

                    } else if (NXOpen::Annotations::LocatorDesignator* attribute
                               = dynamic_cast<NXOpen::Annotations::LocatorDesignator*>(dispInst[0])) {
                        ret_ok = fallbackBaseIntegration(
                            dataTable, part_uid, ugObjId.uid(), "LocatorDesignator");

                    } else if (NXOpen::Annotations::MaterialSpecification* attribute
                               = dynamic_cast<NXOpen::Annotations::MaterialSpecification*>(dispInst[0])) {
                        ret_ok = fallbackBaseIntegration(
                            dataTable, part_uid, ugObjId.uid(), "MaterialSpecification");

                    } else if (NXOpen::Annotations::PartIdentification* attribute
                               = dynamic_cast<NXOpen::Annotations::PartIdentification*>(dispInst[0])) {
                        ret_ok = fallbackBaseIntegration(
                            dataTable, part_uid, ugObjId.uid(), "PartIdentification");

                    } else if (NXOpen::Annotations::PmiInteger* attribute
                               = dynamic_cast<NXOpen::Annotations::PmiInteger*>(dispInst[0])) {
                        ret_ok = fallbackBaseIntegration(dataTable, part_uid, ugObjId.uid(), "PmiInteger");

                    } else if (NXOpen::Annotations::PmiNumber* attribute
                               = dynamic_cast<NXOpen::Annotations::PmiNumber*>(dispInst[0])) {
                        ret_ok = fallbackBaseIntegration(dataTable, part_uid, ugObjId.uid(), "PmiNumber");

                    } else if (NXOpen::Annotations::PmiString* attribute
                               = dynamic_cast<NXOpen::Annotations::PmiString*>(dispInst[0])) {
                        ret_ok = fallbackBaseIntegration(dataTable, part_uid, ugObjId.uid(), "PmiString");

                    } else if (NXOpen::Annotations::PmiUrlNote* attribute
                               = dynamic_cast<NXOpen::Annotations::PmiUrlNote*>(dispInst[0])) {
                        ret_ok
                            = dataPmiUrlNote(dataTable, part_uid, ugObjId.uid(), attribute, manualSelection);

                    } else if (NXOpen::Annotations::PmiUserDefined* attribute
                               = dynamic_cast<NXOpen::Annotations::PmiUserDefined*>(dispInst[0])) {
                        ret_ok
                            = fallbackBaseIntegration(dataTable, part_uid, ugObjId.uid(), "PmiUserDefined");

                    } else if (NXOpen::Annotations::ProcessSpecification* attribute
                               = dynamic_cast<NXOpen::Annotations::ProcessSpecification*>(dispInst[0])) {
                        ret_ok = fallbackBaseIntegration(
                            dataTable, part_uid, ugObjId.uid(), "ProcessSpecification");

                    } else if (NXOpen::Annotations::SpecificNote* attribute
                               = dynamic_cast<NXOpen::Annotations::SpecificNote*>(dispInst[0])) {
                        ret_ok = fallbackBaseIntegration(dataTable, part_uid, ugObjId.uid(), "SpecificNote");

                    } else if (NXOpen::Annotations::SurfaceFinish* attribute
                               = dynamic_cast<NXOpen::Annotations::SurfaceFinish*>(dispInst[0])) {
                        ret_ok = dataSurfaceFinish(
                            dataTable, part_uid, ugObjId.uid(), attribute, manualSelection);

                    } else if (NXOpen::Annotations::PointTarget* pointTarget
                               = dynamic_cast<NXOpen::Annotations::PointTarget*>(dispInst[0])) {
                        ret_ok = dataPointTarget(
                            dataTable, part_uid, ugObjId.uid(), pointTarget, manualSelection);

                    } else if (NXOpen::Annotations::PmiLineWeld* lineWeld
                               = dynamic_cast<NXOpen::Annotations::PmiLineWeld*>(dispInst[0])) {
                        ret_ok
                            = dataPmiLineWeld(dataTable, part_uid, ugObjId.uid(), lineWeld, manualSelection);

                    } else {
                        ret_ok = fallbackBaseIntegration(dataTable, part_uid, ugObjId.uid());
                    }
                } else {
                    Q_DEBUG("The object has no instances.");
                }
            } else {
                Q_DEBUG("The table passed in is invalid.");
            }

            /* ------------------------------------------------------ */

        } else {
            Q_DEBUG("The object is not fully initialized.");
        }
    }
    NXOPEN_CATCH

    return ret_ok;
}


/**
 *
 */
NXOpen::Session* const UGPmiRootClass::session(void) const
{
    UG_INITIALIZE;
    if (!_session) {
        Q_DEBUG_SCOPE log;
        NXOPEN_TRY
        {
            if (NXOpen::Session* session = NXOpen::Session::GetSession()) {

                return session;
            } else {
                Q_DEBUG("The current session is invalid.");
            }
        }
        NXOPEN_CATCH
    }
    return _session;
}


/**
 *
 */
NXOpen::Part* const UGPmiRootClass::workPart(void) const
{
    UG_INITIALIZE;
    if (!_work_part) {
        Q_DEBUG_SCOPE log;
        NXOPEN_TRY
        {
            if (NXOpen::Session* session = NXOpen::Session::GetSession()) {
                if (NXOpen::Part* workPart = session->Parts()->Work()) {

                    return workPart;
                } else {
                    Q_DEBUG("The current work part is invalid.");
                }
            } else {
                Q_DEBUG("The current session is invalid.");
            }
        }
        NXOPEN_CATCH
    }
    return _work_part;
}


/**
 *
 */
NXOpen::Part* const UGPmiRootClass::owningPart(void) const
{
    if (!_owning_part)
        return workPart();
    return _owning_part;
}

NXOpen::Annotations::PmiManager* const UGPmiRootClass::pmiManager(void) const
{
    UG_INITIALIZE;
    if (!_pmi_manager) {
        Q_DEBUG_SCOPE log;
        NXOPEN_TRY
        {
            if (NXOpen::Session* session = NXOpen::Session::GetSession()) {
                if (NXOpen::Part* workPart = session->Parts()->Work()) {
                    if (NXOpen::Annotations::PmiManager* pmiManager = workPart->PmiManager()) {
                        return pmiManager;
                    } else {
                        Q_DEBUG("The current pmi manager is invalid.");
                    }
                } else {
                    Q_DEBUG("The current work part is invalid.");
                }
            } else {
                Q_DEBUG("The current session is invalid.");
            }
        }
        NXOPEN_CATCH
    }
    return _pmi_manager;
}

NXOpen::Annotations::PmiAttributeCollection* UGPmiRootClass::pmiAttributes() const
{
    return (pmiManager() ? pmiManager()->PmiAttributes() : NULL);
}

bool UGPmiRootClass::fallbackBaseIntegration(CADDataTable* dataTable,
                                             const QString& part_uid,
                                             const QString& uidPmi,
                                             const QString& type /* = "Unknown Type"*/) const
{
    Q_DEBUG_SCOPE log;
    Q_WARNING("Using the fall back base integration for: " << type);
    bool ret_ok = false;

    if (dataTable) {

        if (!dataTable->isEmpty())
            dataTable->clear();

        CADDataLine cdl(part_uid, uidPmi);
        cdl.setValueOf(KEY_2009, ChxType::NOTE_3D);
        cdl.setValueOf(KEY_2091, index());

        cdl.setValueOf(KEY_2900, type);

        ret_ok = getLocationInfo(&cdl);

        if (ret_ok)
            ret_ok = dataTable->append(cdl);

        if (!ret_ok)
            dataTable->clear();

    } else {
        Q_DEBUG("The table passed in is invalid.");
    }


    return ret_ok;
}


/**
 *
 */
bool UGPmiRootClass::dataDimension(CADDataTable* dataTable,
                                   const QString& part_uid,
                                   const QString& uidPmi,
                                   NXOpen::Annotations::Dimension* dimension,
                                   const bool& manualSelection /* = false*/) const
{
    UG_INITIALIZE;
    Q_DEBUG_SCOPE log;
    bool ret_ok = false;

    NXOPEN_TRY
    {
        if (_session && _work_part && _owning_part && _pmi_manager && _pmi && !part_uid.isEmpty()
            && !uidPmi.isEmpty()) {

            /* ------------------------------------------------------ */

            if (dataTable && dimension) {

                if (!dataTable->isEmpty())
                    dataTable->clear();

                // ---------------------------------------------------------
                // Remove the #include "ug_dimension_root_class.h",
                // if the dimesion stuff is done inside this class.
                // Don't forget the Makefile!!!;-)
                // ---------------------------------------------------------
                ret_ok = (UGDimensionRootClass().createDimensionDataTable(
                              _part_tag, dimension->Tag(), (*dataTable), manualSelection)
                          == 0);

                for (int i = 0; (i < dataTable->count() && ret_ok); i++) {
                    if (CADDataLine* cdl = (*dataTable)[i]) {
                        cdl->setValueOf(KEY_2091, index());
                        cdl->setCadUid(uidPmi);
                        ret_ok = getLocationInfo(cdl);
                    }
                }

                if (!ret_ok)
                    dataTable->clear();

            } else {
                Q_DEBUG("The objects passed in are invalid.");
            }

            /* ------------------------------------------------------ */

        } else {
            Q_DEBUG("The object is not fully initialized.");
        }
    }
    NXOPEN_CATCH

    return ret_ok;
}


/**
 *
 */
bool UGPmiRootClass::dataFcf(CADDataTable* dataTable,
                             const QString& part_uid,
                             const QString& uidPmi,
                             NXOpen::Annotations::Fcf* fcf,
                             const bool& manualSelection /* = false*/) const
{
    UG_INITIALIZE;
    Q_DEBUG_SCOPE log;
    bool ret_ok = false;

    NXOPEN_TRY
    {
        if (_session && _work_part && _owning_part && _pmi_manager && _pmi && !part_uid.isEmpty()
            && !uidPmi.isEmpty()) {

            /* ------------------------------------------------------ */

            if (dataTable && fcf) {

                if (!dataTable->isEmpty())
                    dataTable->clear();

                // ---------------------------------------------------------
                // Remove the #include "ug_annotation_root_class.h",
                // if the fcf stuff is done inside this class.
                // Don't forget the Makefile!!!;-)
                // ---------------------------------------------------------
                ret_ok = UGAnnotationRootClass().createAnnotationDataTable(
                    _part_tag, fcf->Tag(), dataTable, manualSelection);

                for (int i = 0; (i < dataTable->count() && ret_ok); i++) {
                    if (CADDataLine* cdl = (*dataTable)[i]) {
                        cdl->setValueOf(KEY_2091, index());
                        cdl->setCadUid(uidPmi);
                        ret_ok = getLocationInfo(cdl);
                    }
                }

                if (!ret_ok)
                    dataTable->clear();

            } else {
                Q_DEBUG("The objects passed in are invalid.");
            }

            /* ------------------------------------------------------ */

        } else {
            Q_DEBUG("The object is not fully initialized.");
        }
    }
    NXOPEN_CATCH

    return ret_ok;
}


/**
 *
 */
bool UGPmiRootClass::dataDatum(CADDataTable* dataTable,
                               const QString& part_uid,
                               const QString& uidPmi,
                               NXOpen::Annotations::Datum* datum,
                               const bool& manualSelection /* = false*/) const
{
    UG_INITIALIZE;
    Q_DEBUG_SCOPE log;
    bool ret_ok = false;

    NXOPEN_TRY
    {
        if (_session && _work_part && _owning_part && _pmi_manager && _pmi && !part_uid.isEmpty()
            && !uidPmi.isEmpty()) {

            /* ------------------------------------------------------ */

            if (dataTable && datum) {

                if (!dataTable->isEmpty())
                    dataTable->clear();

                if (NXOpen::Annotations::DraftingDatumFeatureSymbolBuilder* dfsBuilder
                    = _work_part->Annotations()->Datums()->CreateDraftingDatumFeatureSymbolBuilder(datum)) {

                    CADDataLine cdl(part_uid, uidPmi);
                    cdl.setValueOf(KEY_2009, ChxType::NOTE_3D);
                    cdl.setValueOf(KEY_2091, index());

                    QString text(QSTRING_NX(dfsBuilder->Letter()));
                    cdl.setValueOf(KEY_2900, text);

                    ret_ok = getLocationInfo(&cdl);

                    if (ret_ok)
                        ret_ok = dataTable->append(cdl);

                    if (!ret_ok)
                        dataTable->clear();

                    // dfsBuilder->Commit(); // Nothing changed, no commit required!
                    dfsBuilder->Destroy();

                } else {
                    Q_DEBUG("The creation of the drafting datum feature symbol builder has failed.");
                }
            } else {
                Q_DEBUG("The objects passed in are invalid.");
            }

            /* ------------------------------------------------------ */

        } else {
            Q_DEBUG("The object is not fully initialized.");
        }
    }
    NXOPEN_CATCH

    return ret_ok;
}


/**
 * Reads the data of a custom symbol, referred by @a uidPmi and @a cs to @a dataTable
 */
bool UGPmiRootClass::dataCustomSymbol(CADDataTable* dataTable,
                                      const QString& part_uid,
                                      const QString& uidPmi,
                                      NXOpen::Annotations::PmiCustomSymbol* cs) const
{
    UG_INITIALIZE;
    Q_DEBUG_SCOPE log;
    bool ret_ok = false;

    NXOPEN_TRY
    {
        if (_session && _work_part && _owning_part && _pmi_manager && _pmi && !part_uid.isEmpty()
            && !uidPmi.isEmpty()) {

            /* ------------------------------------------------------ */

            if (dataTable && cs) {

                if (!dataTable->isEmpty())
                    dataTable->clear();

                CADDataLine cdl(part_uid, uidPmi);
                cdl.setValueOf(KEY_2009, ChxType::SYMBOL);
                cdl.setValueOf(KEY_2091, index());

                QStringList texts;
                texts << toString(cs->SymbolName());

                if (NXOpen::Annotations::PmiCustomSymbolBuilder* csBuilder
                    = _work_part->Annotations()->CustomSymbols()->CreatePmiCustomSymbolBuilder(cs)) {
                    std::vector<NXOpen::Annotations::MasterSymbolListItemBuilder*> stdVec
                        = csBuilder->Texts()->GetContents();
                    for (int i = 0; i < stdVec.size(); i++) {
                        texts << toString(stdVec[i]->NoteText());
                    }
                } else {
                    Q_DEBUG("The creation of the pmi custom symbol builder has failed.");
                }

                cdl.setValueOf(KEY_2900, texts.join("\r\n"));

                ret_ok = getLocationInfo(&cdl);

                if (ret_ok)
                    ret_ok = dataTable->append(cdl);

                if (!ret_ok)
                    dataTable->clear();

            } else {
                Q_DEBUG("The objects passed in are invalid.");
            }

            /* ------------------------------------------------------ */

        } else {
            Q_DEBUG("The object is not fully initialized.");
        }
    }
    NXOPEN_CATCH

    return ret_ok;
}


/**
 *
 */
bool UGPmiRootClass::dataLabelNote(CADDataTable* dataTable,
                                   const QString& part_uid,
                                   const QString& uidPmi,
                                   NXOpen::Annotations::SimpleDraftingAid* aid,
                                   const bool& manualSelection /* = false*/) const
{
    UG_INITIALIZE;
    Q_DEBUG_SCOPE log;
    bool ret_ok = false;

    NXOPEN_TRY
    {
        if (_session && _work_part && _owning_part && _pmi_manager && _pmi && !part_uid.isEmpty()
            && !uidPmi.isEmpty()) {

            /* ------------------------------------------------------ */

            if (dataTable && aid) {

                if (!dataTable->isEmpty())
                    dataTable->clear();

                CADDataLine cdl(part_uid, uidPmi);
                cdl.setValueOf(KEY_2009, ChxType::NOTE_3D);
                cdl.setValueOf(KEY_2091, index());

                QString text;
                std::vector<NXOpen::NXString> stdVec = aid->GetText();
                foreach (NXOpen::NXString strnx, QVector<NXOpen::NXString>::fromStdVector(stdVec))
                    text += QString("%1%2").arg(text.isEmpty() ? "" : "\r\n").arg(QSTRING_NX(strnx));
                cdl.setValueOf(KEY_2900, text);

                ret_ok = getLocationInfo(&cdl);

                if (ret_ok)
                    ret_ok = dataTable->append(cdl);

                if (!ret_ok)
                    dataTable->clear();

            } else {
                Q_DEBUG("The objects passed in are invalid.");
            }

            /* ------------------------------------------------------ */

        } else {
            Q_DEBUG("The object is not fully initialized.");
        }
    }
    NXOPEN_CATCH

    return ret_ok;
}


/**
 *
 */
bool UGPmiRootClass::dataBalloonNote(CADDataTable* dataTable,
                                     const QString& part_uid,
                                     const QString& uidPmi,
                                     NXOpen::Annotations::BalloonNote* note,
                                     const bool& manualSelection /* = false*/) const
{
    UG_INITIALIZE;
    Q_DEBUG_SCOPE log;
    bool ret_ok = false;

    NXOPEN_TRY
    {
        if (_session && _work_part && _owning_part && _pmi_manager && _pmi && !part_uid.isEmpty()
            && !uidPmi.isEmpty()) {

            /* ------------------------------------------------------ */

            if (dataTable && note) {

                if (!dataTable->isEmpty())
                    dataTable->clear();

                if (NXOpen::Annotations::BalloonNoteBuilder* noteBuilder
                    = _pmi_manager->PmiAttributes()->CreateBalloonNoteBuilder(note)) {

                    CADDataLine cdl(part_uid, uidPmi);
                    cdl.setValueOf(KEY_2009, ChxType::SYMBOL);
                    cdl.setException(ConfigurationXML::sOBJECT_FILTER::EX_ID_SYMBOLS);
                    cdl.setValueOf(KEY_2091, index());

                    QString balloonText(QSTRING_NX(noteBuilder->BalloonText()));
                    cdl.setValueOf(KEY_2900, balloonText);

                    // TODO (SS): QString category(QSTRING_NX(noteBuilder->Category()));
                    // TODO (SS): cdl.setValueOf(???, category);

                    // TODO (SS): QString text;
                    // TODO (SS): std::vector<NXOpen::NXString> stdVec = noteBuilder->GetText();
                    // TODO (SS): foreach (NXOpen::NXString strnx,
                    // TODO (SS):          QVector<NXOpen::NXString>::fromStdVector(stdVec))
                    // TODO (SS):     text += QSTRING_NX(strnx);
                    // TODO (SS): cdl.setValueOf(???, text);

                    // TODO (SS): QString identifier(QSTRING_NX(noteBuilder->Identifier()));
                    // TODO (SS): cdl.setValueOf(???, identifier);

                    // TODO (SS): QString revision(QSTRING_NX(noteBuilder->Revision()));
                    // TODO (SS): cdl.setValueOf(???, revision);

                    ret_ok = getLocationInfo(&cdl);

                    if (ret_ok)
                        ret_ok = dataTable->append(cdl);

                    if (!ret_ok)
                        dataTable->clear();

                    // noteBuilder->Commit(); // Nothing changed, no commit required!
                    noteBuilder->Destroy();

                } else {
                    Q_DEBUG("The creation of the balloon note builder has failed.");
                }
            } else {
                Q_DEBUG("The objects passed in are invalid.");
            }

            /* ------------------------------------------------------ */

        } else {
            Q_DEBUG("The object is not fully initialized.");
        }
    }
    NXOPEN_CATCH

    return ret_ok;
}


/**
 *
 */
bool UGPmiRootClass::dataGeneralNote(CADDataTable* dataTable,
                                     const QString& part_uid,
                                     const QString& uidPmi,
                                     NXOpen::Annotations::GeneralNote* note,
                                     const bool& manualSelection /* = false*/) const
{
    UG_INITIALIZE;
    Q_DEBUG_SCOPE log;
    bool ret_ok = false;

    NXOPEN_TRY
    {
        if (_session && _work_part && _owning_part && _pmi_manager && _pmi && !part_uid.isEmpty()
            && !uidPmi.isEmpty()) {

            /* ------------------------------------------------------ */

            if (dataTable && note) {

                if (!dataTable->isEmpty())
                    dataTable->clear();

                if (NXOpen::Annotations::GeneralNoteBuilder* gnBuilder
                    = _pmi_manager->PmiAttributes()->CreateGeneralNoteBuilder(note)) {

                    CADDataLine cdl(part_uid, uidPmi);
                    cdl.setValueOf(KEY_2009, ChxType::NOTE_3D);
                    cdl.setValueOf(KEY_2091, index());

                    // TODO (SS): QString title(QSTRING_NX(gnBuilder->Title()));
                    // TODO (SS): cdl.setValueOf(???, title);

                    // TODO (SS): QString category(QSTRING_NX(gnBuilder->Category()));
                    // TODO (SS): cdl.setValueOf(???, category);

                    // TODO (SS): QString identifier(QSTRING_NX(gnBuilder->Identifier()));
                    // TODO (SS): cdl.setValueOf(???, identifier);

                    // TODO (SS): QString revision(QSTRING_NX(gnBuilder->Revision()));
                    // TODO (SS): cdl.setValueOf(???, revision);

                    QString text;
                    std::vector<NXOpen::NXString> stdVec = gnBuilder->GetText();
                    foreach (NXOpen::NXString strnx, QVector<NXOpen::NXString>::fromStdVector(stdVec))
                        text += QSTRING_NX(strnx);
                    cdl.setValueOf(KEY_2900, text);

                    ret_ok = getLocationInfo(&cdl);

                    if (ret_ok)
                        ret_ok = dataTable->append(cdl);

                    if (!ret_ok)
                        dataTable->clear();

                    // gnBuilder->Commit(); // Nothing changed, no commit required!
                    gnBuilder->Destroy();

                } else {
                    Q_DEBUG("The creation of the general note builder has failed.");
                }
            } else {
                Q_DEBUG("The objects passed in are invalid.");
            }

            /* ------------------------------------------------------ */

        } else {
            Q_DEBUG("The object is not fully initialized.");
        }
    }
    NXOPEN_CATCH

    return ret_ok;
}


/**
 *
 */
bool UGPmiRootClass::dataPmiUrlNote(CADDataTable* dataTable,
                                    const QString& part_uid,
                                    const QString& uidPmi,
                                    NXOpen::Annotations::PmiUrlNote* note,
                                    const bool& manualSelection /* = false*/) const
{
    UG_INITIALIZE;
    Q_DEBUG_SCOPE log;
    bool ret_ok = false;

    NXOPEN_TRY
    {
        if (_session && _work_part && _owning_part && _pmi_manager && _pmi && !part_uid.isEmpty()
            && !uidPmi.isEmpty()) {

            /* ------------------------------------------------------ */

            if (dataTable && note) {

                if (!dataTable->isEmpty())
                    dataTable->clear();

                if (NXOpen::Annotations::PmiUrlNoteBuilder* punBuilder
                    = _pmi_manager->PmiAttributes()->CreatePmiUrlNoteBuilder(note)) {

                    CADDataLine cdl(part_uid, uidPmi);
                    cdl.setValueOf(KEY_2009, ChxType::NOTE_3D);
                    cdl.setValueOf(KEY_2091, index());

                    QString title(QSTRING_NX(punBuilder->Title()));
                    // TODO (SS): cdl.setValueOf(???, title);

                    QString urlValue(QSTRING_NX(punBuilder->UrlValue()));
                    // TODO (SS): cdl.setValueOf(???, urlValue);

                    cdl.setValueOf(KEY_2900, QString("%1: %2").arg(title).arg(urlValue));

                    ret_ok = getLocationInfo(&cdl);

                    if (ret_ok)
                        ret_ok = dataTable->append(cdl);

                    if (!ret_ok)
                        dataTable->clear();

                    // punBuilder->Commit(); // Nothing changed, no commit required!
                    punBuilder->Destroy();

                } else {
                    Q_DEBUG("The creation of the pmi url note builder has failed.");
                }
            } else {
                Q_DEBUG("The objects passed in are invalid.");
            }

            /* ------------------------------------------------------ */

        } else {
            Q_DEBUG("The object is not fully initialized.");
        }
    }
    NXOPEN_CATCH

    return ret_ok;
}


/**
 *
 */
bool UGPmiRootClass::dataSurfaceFinish(CADDataTable* dataTable,
                                       const QString& part_uid,
                                       const QString& uidPmi,
                                       NXOpen::Annotations::SurfaceFinish* sf,
                                       const bool& manualSelection /* = false*/) const
{
    UG_INITIALIZE;
    Q_DEBUG_SCOPE log;
    bool ret_ok = false;

    NXOPEN_TRY
    {
        if (_session && _work_part && _owning_part && _pmi_manager && _pmi && !part_uid.isEmpty()
            && !uidPmi.isEmpty()) {

            /* ------------------------------------------------------ */

            if (dataTable && sf) {

                if (!dataTable->isEmpty())
                    dataTable->clear();

                if (NXOpen::Annotations::SurfaceFinishBuilder* sfBuilder
                    = _pmi_manager->PmiAttributes()->CreateSurfaceFinishBuilder(sf)) {

                    CADDataLine cdl(part_uid, uidPmi);

                    // TODO (SS): Use more accuratee ChxType.
                    //            - SURFACE_RZ, SURFACE_RT, SURFACE_RA, SURFACE_PT, SURFACE_RMAX
                    cdl.setValueOf(KEY_2009, ChxType::SYMBOL);
                    cdl.setValueOf(KEY_2091, index());

                    // TODO (SS): QString title(QSTRING_NX(sfBuilder->Title()));
                    // TODO (SS): cdl.setValueOf(???, title);

                    // TODO (SS): QString standard;
                    // TODO (SS): switch (sfBuilder->Standard()) {
                    // TODO (SS):     case NXOpen::Annotations::SurfaceFinishBuilder::StandardTypeAnsi:
                    // TODO (SS):         standard = "ANSI";
                    // TODO (SS):         break;
                    // TODO (SS):     case NXOpen::Annotations::SurfaceFinishBuilder::StandardTypeIso:
                    // TODO (SS):         standard = "ISO";
                    // TODO (SS):         break;
                    // TODO (SS):     case NXOpen::Annotations::SurfaceFinishBuilder::StandardTypeJis:
                    // TODO (SS):         standard = "JIS";
                    // TODO (SS):         break;
                    // TODO (SS):     case NXOpen::Annotations::SurfaceFinishBuilder::StandardTypeDin:
                    // TODO (SS):         standard = "DIN";
                    // TODO (SS):         break;
                    // TODO (SS):     case NXOpen::Annotations::SurfaceFinishBuilder::StandardTypeIso2002:
                    // TODO (SS):         standard = "ISO 2002";
                    // TODO (SS):         break;
                    // TODO (SS):     case NXOpen::Annotations::SurfaceFinishBuilder::StandardTypeDin2002:
                    // TODO (SS):         standard = "DIN 2002";
                    // TODO (SS):         break;
                    // TODO (SS):     case NXOpen::Annotations::SurfaceFinishBuilder::StandardTypeGb:
                    // TODO (SS):         standard = "GB131-93";
                    // TODO (SS):         break;
                    // TODO (SS):     case NXOpen::Annotations::SurfaceFinishBuilder::StandardTypeEskd:
                    // TODO (SS):         standard = "ESKD";
                    // TODO (SS):         break;
                    // TODO (SS): }
                    // TODO (SS): cdl.setValueOf(???, standard);

                    // ---------------------------------------------------------
                    // Commented in for Siemens LMV (Mantis: #0005641)
                    // ---------------------------------------------------------
                    QString finish;
                    switch (sfBuilder->Finish()) {
                    case NXOpen::Annotations::SurfaceFinishBuilder::FinishTypeBasic:
                        finish = "Basic.";
                        break;
                    case NXOpen::Annotations::SurfaceFinishBuilder::FinishTypeModifier:
                        finish = "Modifier.";
                        break;
                    case NXOpen::Annotations::SurfaceFinishBuilder::FinishTypeModifierAllAround:
                        finish = "Modifier, All Around.";
                        break;
                    case NXOpen::Annotations::SurfaceFinishBuilder::FinishTypeMaterialRemovalRequired:
                        finish = "Material Removal Required.";
                        break;
                    case NXOpen::Annotations::SurfaceFinishBuilder::FinishTypeModifierMaterialRemovalRequired:
                        finish = "Modifier, Material Removal Required.";
                        break;
                    case NXOpen::Annotations::SurfaceFinishBuilder::
                        FinishTypeModifierMaterialRemovalRequiredAllAround:
                        finish = "Modifier, Material Removal Required, All Around.";
                        break;
                    case NXOpen::Annotations::SurfaceFinishBuilder::FinishTypeMaterialRemovalProhibited:
                        finish = "Material Removal Prohibited.";
                        break;
                    case NXOpen::Annotations::SurfaceFinishBuilder::
                        FinishTypeModifierMaterialRemovalProhibited:
                        finish = "Modifier, Material Removal Prohibited.";
                        break;
                    case NXOpen::Annotations::SurfaceFinishBuilder::
                        FinishTypeModifierMaterialRemovalProhibitedAllAround:
                        finish = "Modifier, Material Removal Prohibited, All Around.";
                        break;
                    }
                    // ---
                    // ---------------------------------------------------------
                    // TODO (SS): cdl.setValueOf(???, finish);

                    QString a1(QSTRING_NX(sfBuilder->A1()));
                    // TODO (SS): cdl.setValueOf(???, a1);

                    QString a2(QSTRING_NX(sfBuilder->A2()));
                    // TODO (SS): cdl.setValueOf(???, a2);

                    QString b(QSTRING_NX(sfBuilder->B()));
                    // TODO (SS): cdl.setValueOf(???, b);

                    QString c(QSTRING_NX(sfBuilder->C()));
                    // TODO (SS): cdl.setValueOf(???, c);

                    QString d(QSTRING_NX(sfBuilder->D()));
                    // TODO (SS): cdl.setValueOf(???, d);

                    QString e(QSTRING_NX(sfBuilder->E()));
                    // TODO (SS): cdl.setValueOf(???, e);

                    QString f1(QSTRING_NX(sfBuilder->F1()));
                    // TODO (SS): cdl.setValueOf(???, f1);

                    QString f2(QSTRING_NX(sfBuilder->F2()));
                    // TODO (SS): cdl.setValueOf(???, f2);

                    QString key2900;
                    // ---------------------------------------------------------
                    // Added for Siemens LMV (Mantis: #0005641)
                    // ---------------------------------------------------------
                    if (!finish.isEmpty())
                        key2900 += QString("%1%2").arg(key2900.isEmpty() ? "" : " | ").arg(finish);
                    // ---
                    // ---------------------------------------------------------
                    if (!a1.isEmpty())
                        key2900 += QString("%1%2").arg(key2900.isEmpty() ? "" : " | ").arg(a1);
                    if (!a2.isEmpty() && a1 != a2)
                        key2900 += QString("%1%2").arg(key2900.isEmpty() ? "" : " | ").arg(a2);
                    if (!b.isEmpty())
                        key2900 += QString("%1%2").arg(key2900.isEmpty() ? "" : " | ").arg(b);
                    if (!c.isEmpty())
                        key2900 += QString("%1%2").arg(key2900.isEmpty() ? "" : " | ").arg(c);
                    if (!d.isEmpty())
                        key2900 += QString("%1%2").arg(key2900.isEmpty() ? "" : " | ").arg(d);
                    if (!e.isEmpty())
                        key2900 += QString("%1%2").arg(key2900.isEmpty() ? "" : " | ").arg(e);
                    if (!f1.isEmpty())
                        key2900 += QString("%1%2").arg(key2900.isEmpty() ? "" : " | ").arg(f1);
                    if (!f2.isEmpty() && f1 != f2)
                        key2900 += QString("%1%2").arg(key2900.isEmpty() ? "" : " | ").arg(f2);

                    cdl.setValueOf(KEY_2900, key2900);

                    ret_ok = getLocationInfo(&cdl);

                    if (ret_ok)
                        ret_ok = dataTable->append(cdl);

                    if (!ret_ok)
                        dataTable->clear();

                    // sfBuilder->Commit(); // Nothing changed, no commit required!
                    sfBuilder->Destroy();

                } else {
                    Q_DEBUG("The creation of the surface finish builder has failed.");
                }
            } else {
                Q_DEBUG("The objects passed in are invalid.");
            }

            /* ------------------------------------------------------ */

        } else {
            Q_DEBUG("The object is not fully initialized.");
        }
    }
    NXOPEN_CATCH

    return ret_ok;
}


/**
 *
 */
bool UGPmiRootClass::dataPointTarget(CADDataTable* dataTable,
                                     const QString& part_uid,
                                     const QString& uidPmi,
                                     NXOpen::Annotations::PointTarget* point,
                                     const bool& manualSelection /* = false*/) const
{
    UG_INITIALIZE;
    Q_DEBUG_SCOPE log;
    bool ret_ok = false;

    NXOPEN_TRY
    {
        if (_session && _work_part && _owning_part && _pmi_manager && _pmi && !part_uid.isEmpty()
            && !uidPmi.isEmpty()) {

            /* ------------------------------------------------------ */

            if (dataTable && point) {

                if (!dataTable->isEmpty())
                    dataTable->clear();

                if (NXOpen::Annotations::PmiDatumTargetBuilder* pdtBuilder
                    = _work_part->Annotations()->CreatePmiDatumTargetBuilder(point)) {

                    CADDataLine cdl(part_uid, uidPmi);
                    cdl.setValueOf(KEY_2009, ChxType::SYMBOL);
                    cdl.setValueOf(KEY_2091, index());

                    QString label(QSTRING_NX(pdtBuilder->Label()));
                    // TODO (SS): cdl.setValueOf(???, label);

                    int index = pdtBuilder->Index();
                    // TODO (SS): cdl.setValueOf(???, index);

                    cdl.setValueOf(KEY_2900, QString("%1%2").arg(label).arg(index));

                    ret_ok = getLocationInfo(&cdl);

                    if (ret_ok)
                        ret_ok = dataTable->append(cdl);

                    if (!ret_ok)
                        dataTable->clear();

                    // pdtBuilder->Commit(); // Nothing changed, no commit required!
                    pdtBuilder->Destroy();

                } else {
                    Q_DEBUG("The creation of the pmi datum target builder has failed.");
                }
            } else {
                Q_DEBUG("The objects passed in are invalid.");
            }

            /* ------------------------------------------------------ */

        } else {
            Q_DEBUG("The object is not fully initialized.");
        }
    }
    NXOPEN_CATCH

    return ret_ok;
}


/**
 *
 */
bool UGPmiRootClass::dataPmiLineWeld(CADDataTable* dataTable,
                                     const QString& part_uid,
                                     const QString& uidPmi,
                                     NXOpen::Annotations::PmiLineWeld* lineWeld,
                                     const bool& manualSelection /* = false*/) const
{
    UG_INITIALIZE;
    Q_DEBUG_SCOPE log;
    bool ret_ok = false;

    NXOPEN_TRY
    {
        if (_session && _work_part && _owning_part && _pmi_manager && _pmi && !part_uid.isEmpty()
            && !uidPmi.isEmpty()) {

            /* ------------------------------------------------------ */

            if (dataTable && lineWeld) {

                if (!dataTable->isEmpty())
                    dataTable->clear();

                if (NXOpen::Annotations::PmiLineWeldBuilder* plwBuilder
                    = _work_part->Annotations()->Welds()->CreatePmiLineWeldBuilder(lineWeld)) {

                    CADDataLine cdl(part_uid, uidPmi);
                    cdl.setValueOf(KEY_2009, ChxType::WELD_SYMBOL);
                    cdl.setValueOf(KEY_2091, index());

                    QMap<NXOpen::Annotations::LineWeldDataBuilder::FinishSymbolType, QString>
                        finishSymbolType;
                    finishSymbolType[NXOpen::Annotations::LineWeldDataBuilder::FinishSymbolTypeNone]
                        = ""; // "None";
                    finishSymbolType[NXOpen::Annotations::LineWeldDataBuilder::FinishSymbolTypeChipping]
                        = "C"; // "Chipping";
                    finishSymbolType[NXOpen::Annotations::LineWeldDataBuilder::FinishSymbolTypeGrinding]
                        = "G"; // "Grinding";
                    finishSymbolType[NXOpen::Annotations::LineWeldDataBuilder::FinishSymbolTypeHammering]
                        = "H"; // "Hammering";
                    finishSymbolType[NXOpen::Annotations::LineWeldDataBuilder::FinishSymbolTypeMachining]
                        = "M"; // "Machining";
                    finishSymbolType[NXOpen::Annotations::LineWeldDataBuilder::FinishSymbolTypeRolling]
                        = "R"; // "Rolling";
                    finishSymbolType[NXOpen::Annotations::LineWeldDataBuilder::FinishSymbolTypePeening]
                        = "P"; // "Peening";

                    QMap<NXOpen::Annotations::LineWeldDataBuilder::ContourSymbolType, QString>
                        contourSymbolType;
                    contourSymbolType[NXOpen::Annotations::LineWeldDataBuilder::ContourSymbolTypeNone]
                        = ""; // "None";
                    contourSymbolType[NXOpen::Annotations::LineWeldDataBuilder::ContourSymbolTypeConvex]
                        = "Convex";
                    contourSymbolType[NXOpen::Annotations::LineWeldDataBuilder::ContourSymbolTypeFlat]
                        = "Flat";
                    contourSymbolType[NXOpen::Annotations::LineWeldDataBuilder::ContourSymbolTypeConcave]
                        = "Concave";
                    contourSymbolType
                        [NXOpen::Annotations::LineWeldDataBuilder::ContourSymbolTypeBlendedToesIsoAndDinOnly]
                        = "Blended Toes (ISO, DIN only)";
                    contourSymbolType[NXOpen::Annotations::LineWeldDataBuilder::
                                          ContourSymbolTypeBackingStripPermanentIsoAndDinOnly]
                        = "Backing Strip Permanent (ISO, DIN only)";
                    contourSymbolType[NXOpen::Annotations::LineWeldDataBuilder::
                                          ContourSymbolTypeBackingStripRemovableIsoAndDinOnly]
                        = "Backing Strip Removable (ISO, DIN only)";
                    contourSymbolType[NXOpen::Annotations::LineWeldDataBuilder::ContourSymbolTypeFlush]
                        = "Flush";
                    contourSymbolType
                        [NXOpen::Annotations::LineWeldDataBuilder::ContourSymbolTypeMachiningGradedJunction]
                        = "Machining Graded Junction";

                    QMap<NXOpen::Annotations::LineWeldDataBuilder::WeldSizeCodeType, QString>
                        weldSizeCodeType;
                    weldSizeCodeType[NXOpen::Annotations::LineWeldDataBuilder::WeldSizeCodeTypeNone]
                        = ""; // "None";
                    weldSizeCodeType[NXOpen::Annotations::LineWeldDataBuilder::WeldSizeCodeTypeA] = "a";
                    weldSizeCodeType[NXOpen::Annotations::LineWeldDataBuilder::WeldSizeCodeTypeC] = "c";
                    weldSizeCodeType[NXOpen::Annotations::LineWeldDataBuilder::WeldSizeCodeTypeD] = "d";
                    weldSizeCodeType[NXOpen::Annotations::LineWeldDataBuilder::WeldSizeCodeTypeS] = "s";
                    weldSizeCodeType[NXOpen::Annotations::LineWeldDataBuilder::WeldSizeCodeTypeZ] = "z";
                    weldSizeCodeType[NXOpen::Annotations::LineWeldDataBuilder::WeldSizeCodeTypeP] = "p";

                    QMap<NXOpen::Annotations::LineWeldDataBuilder::WeldSymbolType, QString> weldSymbolType;
                    weldSymbolType[NXOpen::Annotations::LineWeldDataBuilder::WeldSymbolTypeNone]
                        = ""; // "None";
                    weldSymbolType
                        [NXOpen::Annotations::LineWeldDataBuilder::WeldSymbolTypeButtWeldWithRaisedEdges]
                        = "Butt Weld with Raised Edges";
                    weldSymbolType[NXOpen::Annotations::LineWeldDataBuilder::WeldSymbolTypeSingleFlange]
                        = "Single Flange";
                    weldSymbolType[NXOpen::Annotations::LineWeldDataBuilder::WeldSymbolTypeSquareButt]
                        = "Square Butt";
                    weldSymbolType[NXOpen::Annotations::LineWeldDataBuilder::WeldSymbolTypeVButt] = "V-Butt";
                    weldSymbolType
                        [NXOpen::Annotations::LineWeldDataBuilder::WeldSymbolTypeVButtWithBroadRootFace]
                        = "V-Butt with Broad Root Face";
                    weldSymbolType[NXOpen::Annotations::LineWeldDataBuilder::WeldSymbolTypeBevelButt]
                        = "Bevel Butt";
                    weldSymbolType
                        [NXOpen::Annotations::LineWeldDataBuilder::WeldSymbolTypeBevelButtWithBroadRootFace]
                        = "Bevel Butt with Broad Root Face";
                    weldSymbolType[NXOpen::Annotations::LineWeldDataBuilder::WeldSymbolTypeUButt] = "U-Butt";
                    weldSymbolType[NXOpen::Annotations::LineWeldDataBuilder::WeldSymbolTypeJButt] = "J-Butt";
                    weldSymbolType[NXOpen::Annotations::LineWeldDataBuilder::WeldSymbolTypeFlareV]
                        = "Flare V";
                    weldSymbolType[NXOpen::Annotations::LineWeldDataBuilder::WeldSymbolTypeFlareBevel]
                        = "Flare Bevel";
                    weldSymbolType[NXOpen::Annotations::LineWeldDataBuilder::WeldSymbolTypeFillet] = "Fillet";
                    weldSymbolType[NXOpen::Annotations::LineWeldDataBuilder::WeldSymbolTypePlugSlot]
                        = "Plug Slot";
                    weldSymbolType[NXOpen::Annotations::LineWeldDataBuilder::WeldSymbolTypeEdge] = "Edge";
                    weldSymbolType[NXOpen::Annotations::LineWeldDataBuilder::WeldSymbolTypeSpot] = "Spot";
                    weldSymbolType[NXOpen::Annotations::LineWeldDataBuilder::WeldSymbolTypeSeam] = "Seam";
                    weldSymbolType[NXOpen::Annotations::LineWeldDataBuilder::WeldSymbolTypeSteepFlankedVButt]
                        = "Steep Flanked V-Butt";
                    weldSymbolType
                        [NXOpen::Annotations::LineWeldDataBuilder::WeldSymbolTypeSteepFlankedBevelButt]
                        = "Steep Flanked Bevel Butt";
                    weldSymbolType[NXOpen::Annotations::LineWeldDataBuilder::WeldSymbolTypeBacking]
                        = "Backing";
                    weldSymbolType[NXOpen::Annotations::LineWeldDataBuilder::WeldSymbolTypeSurfaceJoint]
                        = "Surface Joint";
                    weldSymbolType[NXOpen::Annotations::LineWeldDataBuilder::WeldSymbolTypeInclinedJoint]
                        = "Inclined Joint";
                    weldSymbolType[NXOpen::Annotations::LineWeldDataBuilder::WeldSymbolTypeFoldJoint]
                        = "Fold Joint";
                    weldSymbolType[NXOpen::Annotations::LineWeldDataBuilder::WeldSymbolTypeStud] = "Stud";
                    weldSymbolType[NXOpen::Annotations::LineWeldDataBuilder::WeldSymbolTypeSurfacing]
                        = "Surfacing";
                    weldSymbolType[NXOpen::Annotations::LineWeldDataBuilder::WeldSymbolTypeIntermittent]
                        = "Intermittent";
                    weldSymbolType[NXOpen::Annotations::LineWeldDataBuilder::WeldSymbolTypeBackingPlate]
                        = "Backing Plate";
                    weldSymbolType[NXOpen::Annotations::LineWeldDataBuilder::WeldSymbolTypeNotSpecified]
                        = "Not Specified";

                    // ---------------------------------------------------------
                    // Other Side ...
                    // ---------------------------------------------------------
                    NXOpen::Annotations::LineWeldDataBuilder::WeldSymbolType weldSymbolTypeOtherSide
                        = NXOpen::Annotations::LineWeldDataBuilder::WeldSymbolTypeNone;
                    QList<QStringList> otherSide; // Lines with values per line ...
                    if (NXOpen::Annotations::LineWeldDataBuilder* lwdBuilder
                        = plwBuilder->OtherSideWeldData()) {
                        if ((weldSymbolTypeOtherSide = lwdBuilder->WeldSymbol())
                            != NXOpen::Annotations::LineWeldDataBuilder::WeldSymbolTypeNone) {

                            if (finishSymbolType.contains(lwdBuilder->FinishSymbol())
                                && !finishSymbolType[lwdBuilder->FinishSymbol()].isEmpty())
                                otherSide << QStringList(finishSymbolType[lwdBuilder->FinishSymbol()]);

                            if (contourSymbolType.contains(lwdBuilder->ContourSymbol())
                                && !contourSymbolType[lwdBuilder->ContourSymbol()].isEmpty())
                                otherSide << QStringList(contourSymbolType[lwdBuilder->ContourSymbol()]);

                            if (!QSTRING_NX(lwdBuilder->GrooveCountersinkAngle()).isEmpty())
                                otherSide << QStringList(QSTRING_NX(lwdBuilder->GrooveCountersinkAngle()));

                            QStringList compoundWeld;
                            if (lwdBuilder->IsCompound()) {
                                if (weldSizeCodeType.contains(lwdBuilder->CompoundWeldSizeCode())
                                    && !weldSizeCodeType[lwdBuilder->CompoundWeldSizeCode()].isEmpty())
                                    compoundWeld << weldSizeCodeType[lwdBuilder->CompoundWeldSizeCode()];
                                if (!QSTRING_NX(lwdBuilder->CompoundWeldSize()).isEmpty())
                                    compoundWeld << QSTRING_NX(lwdBuilder->CompoundWeldSize());
                            }
                            if (!QSTRING_NX(lwdBuilder->NumberRootDepth()).isEmpty())
                                compoundWeld << QSTRING_NX(lwdBuilder->NumberRootDepth());
                            if (!compoundWeld.isEmpty())
                                otherSide << compoundWeld;

                            QStringList weld;
                            if (weldSizeCodeType.contains(lwdBuilder->WeldSizeCode())
                                && !weldSizeCodeType[lwdBuilder->WeldSizeCode()].isEmpty())
                                weld << weldSizeCodeType[lwdBuilder->WeldSizeCode()];
                            if (!QSTRING_NX(lwdBuilder->WeldSize()).isEmpty())
                                weld << QSTRING_NX(lwdBuilder->WeldSize());
                            if (weldSymbolType.contains(lwdBuilder->WeldSymbol())
                                && !weldSymbolType[lwdBuilder->WeldSymbol()].isEmpty())
                                weld << weldSymbolType[lwdBuilder->WeldSymbol()];
                            if (!QSTRING_NX(lwdBuilder->LengthPitch()).isEmpty())
                                weld << QSTRING_NX(lwdBuilder->LengthPitch());
                            if (!weld.isEmpty())
                                otherSide << weld;
                        }
                    }

                    // ---------------------------------------------------------
                    // Weld Line ...
                    // ---------------------------------------------------------
                    QStringList weldLine;
                    switch (plwBuilder->FieldWeld()) {
                    case NXOpen::Annotations::LineWeldBuilder::FieldWeldTypePlain:
                        weldLine << "Plain";
                        break;
                    case NXOpen::Annotations::LineWeldBuilder::FieldWeldTypeTopField:
                        weldLine << "Top Field";
                        break;
                    case NXOpen::Annotations::LineWeldBuilder::FieldWeldTypeTopFieldSimpleFlag:
                        weldLine << "Top Field Simple Flag";
                        break;
                    }

                    switch (plwBuilder->IdLine()) {
                    case NXOpen::Annotations::LineWeldBuilder::IdLineTypePlain:
                        weldLine << "Plain";
                        break;
                    case NXOpen::Annotations::LineWeldBuilder::IdLineTypeIdLineAbove:
                        weldLine << "ID Line Above";
                        break;
                    case NXOpen::Annotations::LineWeldBuilder::IdLineTypeIdLineBelow:
                        weldLine << "ID Line Below";
                        break;
                    case NXOpen::Annotations::LineWeldBuilder::IdLineTypeCenteredSpotWeld:
                        weldLine << "Centered Spot Weld";
                        break;
                    case NXOpen::Annotations::LineWeldBuilder::IdLineTypeCenteredSeamWeld:
                        weldLine << "Centered Seam Weld";
                        break;
                    }

                    switch (plwBuilder->StaggeredWeld()) {
                    case NXOpen::Annotations::LineWeldBuilder::StaggeredWeldTypeNoStaggeredSymbol:
                        weldLine << "No Staggered Symbol";
                        break;
                    case NXOpen::Annotations::LineWeldBuilder::StaggeredWeldTypeStaggeredSymbol:
                        weldLine << "Staggered Symbol";
                        break;
                    case NXOpen::Annotations::LineWeldBuilder::StaggeredWeldTypeStaggeredSymbolWeldSide:
                        weldLine << "Staggered Symbol Weld Side";
                        break;
                    }

                    switch (plwBuilder->Tail()) {
                    case NXOpen::Annotations::LineWeldBuilder::TailTypeNoTail:
                        // weldLine << "No Tail";
                        break;
                    case NXOpen::Annotations::LineWeldBuilder::TailTypeTail:
                        weldLine << "Tail";
                        break;
                    case NXOpen::Annotations::LineWeldBuilder::TailTypeBox:
                        weldLine << "Box";
                        break;
                    }

                    QString text;
                    foreach (NXOpen::NXString s,
                             QVector<NXOpen::NXString>::fromStdVector(plwBuilder->GetReference()))
                        text += QString("%1%2").arg(text.isEmpty() ? "" : "\r\n").arg(QSTRING_NX(s));
                    if (!text.isEmpty())
                        weldLine << text;

                    // ---------------------------------------------------------
                    // Arrow Side ...
                    // ---------------------------------------------------------
                    NXOpen::Annotations::LineWeldDataBuilder::WeldSymbolType weldSymbolTypeArrowSide
                        = NXOpen::Annotations::LineWeldDataBuilder::WeldSymbolTypeNone;
                    QList<QStringList> arrowSide; // Lines with values per line ...
                    if (NXOpen::Annotations::LineWeldDataBuilder* lwdBuilder
                        = plwBuilder->ArrowSideWeldData()) {
                        if ((weldSymbolTypeArrowSide = lwdBuilder->WeldSymbol())
                            != NXOpen::Annotations::LineWeldDataBuilder::WeldSymbolTypeNone) {

                            QStringList weld;
                            if (weldSizeCodeType.contains(lwdBuilder->WeldSizeCode())
                                && !weldSizeCodeType[lwdBuilder->WeldSizeCode()].isEmpty())
                                weld << weldSizeCodeType[lwdBuilder->WeldSizeCode()];
                            if (!QSTRING_NX(lwdBuilder->WeldSize()).isEmpty())
                                weld << QSTRING_NX(lwdBuilder->WeldSize());
                            if (weldSymbolType.contains(lwdBuilder->WeldSymbol())
                                && !weldSymbolType[lwdBuilder->WeldSymbol()].isEmpty())
                                weld << weldSymbolType[lwdBuilder->WeldSymbol()];
                            if (!QSTRING_NX(lwdBuilder->LengthPitch()).isEmpty())
                                weld << QSTRING_NX(lwdBuilder->LengthPitch());
                            if (!weld.isEmpty())
                                arrowSide << weld;

                            QStringList compoundWeld;
                            if (lwdBuilder->IsCompound()) {
                                if (weldSizeCodeType.contains(lwdBuilder->CompoundWeldSizeCode())
                                    && !weldSizeCodeType[lwdBuilder->CompoundWeldSizeCode()].isEmpty())
                                    compoundWeld << weldSizeCodeType[lwdBuilder->CompoundWeldSizeCode()];
                                if (!QSTRING_NX(lwdBuilder->CompoundWeldSize()).isEmpty())
                                    compoundWeld << QSTRING_NX(lwdBuilder->CompoundWeldSize());
                            }
                            if (!QSTRING_NX(lwdBuilder->NumberRootDepth()).isEmpty())
                                compoundWeld << QSTRING_NX(lwdBuilder->NumberRootDepth());
                            if (!compoundWeld.isEmpty())
                                arrowSide << compoundWeld;

                            if (!QSTRING_NX(lwdBuilder->GrooveCountersinkAngle()).isEmpty())
                                arrowSide << QStringList(QSTRING_NX(lwdBuilder->GrooveCountersinkAngle()));

                            if (contourSymbolType.contains(lwdBuilder->ContourSymbol())
                                && !contourSymbolType[lwdBuilder->ContourSymbol()].isEmpty())
                                arrowSide << QStringList(contourSymbolType[lwdBuilder->ContourSymbol()]);

                            if (finishSymbolType.contains(lwdBuilder->FinishSymbol())
                                && !finishSymbolType[lwdBuilder->FinishSymbol()].isEmpty())
                                arrowSide << QStringList(finishSymbolType[lwdBuilder->FinishSymbol()]);
                        }
                    }

                    // ---------------------------------------------------------
                    // Bind the values - Lines with values per line ...
                    // ---------------------------------------------------------
                    if (weldSymbolTypeOtherSide
                            == NXOpen::Annotations::LineWeldDataBuilder::WeldSymbolTypeNone
                        || weldSymbolTypeArrowSide
                            == NXOpen::Annotations::LineWeldDataBuilder::WeldSymbolTypeSpot
                        || weldSymbolTypeArrowSide
                            == NXOpen::Annotations::LineWeldDataBuilder::WeldSymbolTypeSeam)
                        otherSide.clear();

                    if (weldSymbolTypeArrowSide
                            == NXOpen::Annotations::LineWeldDataBuilder::WeldSymbolTypeNone
                        || weldSymbolTypeOtherSide
                            == NXOpen::Annotations::LineWeldDataBuilder::WeldSymbolTypeSpot
                        || weldSymbolTypeOtherSide
                            == NXOpen::Annotations::LineWeldDataBuilder::WeldSymbolTypeSeam)
                        arrowSide.clear();

                    QString key2900;
                    foreach (QStringList l, QList<QStringList>() << otherSide << weldLine << arrowSide) {
                        QString line;
                        for (int i = 0; i < l.count(); i++) {
                            // ---------------------------------------------------------
                            // Check for attribute values ...
                            // ---------------------------------------------------------
                            if (l[i].contains(QRegExp(".*<.+@.+>.*"))) {
                                if (NXOpen::Annotations::AssociativeText* associative_text
                                    = _work_part->Annotations()->CreateAssociativeText()) {
                                    l[i] = QSTRING_NX(
                                        associative_text->GetEvaluatedText(lineWeld, NXSTRING_Q(l[i])));
                                    delete associative_text;
                                }
                            }
                            line += QString("%1%2").arg(line.isEmpty() ? "" : " | ").arg(l[i]);
                        }
                        key2900 += QString("%1%2").arg(key2900.isEmpty() ? "" : "\r\n").arg(l.join(" | "));
                    }
                    cdl.setValueOf(KEY_2900, key2900);

                    ret_ok = getLocationInfo(&cdl);

                    if (ret_ok)
                        ret_ok = dataTable->append(cdl);

                    if (!ret_ok)
                        dataTable->clear();

                    // plwBuilder->Commit(); // Nothing changed, no commit required!
                    plwBuilder->Destroy();

                } else {
                    Q_DEBUG("The creation of the pmi line weld builder has failed.");
                }
            } else {
                Q_DEBUG("The objects passed in are invalid.");
            }

            /* ------------------------------------------------------ */

        } else {
            Q_DEBUG("The object is not fully initialized.");
        }
    }
    NXOPEN_CATCH

    return ret_ok;
}


/**
 *
 */
// bool UGPmiRootClass::dataXXX(CADDataTable*  dataTable,
//                              const QString& part_uid,
//                              const QString& uid_pmi,
//                              Point*         point,
//                              const bool&    manual_selection/* = false*/) const
// {
//     Q_DEBUG_SCOPE log;
//     bool ret_ok = false;
//
//     try {
//         if (_session && _work_part && _owning_part && _pmi_manager && _pmi
//             && !part_uid.isEmpty() && !uid_pmi.isEmpty()) {
//
//             /* ------------------------------------------------------ */
//
//             if (data_table && point) {
//
//                 if (!data_table->isEmpty())
//                     data_table->clear();
//
//                 /// TODO (SS): ...
//                 CADDataLine cdl(part_uid, uid_pmi);
//                 cdl.setValueOf(KEY_2009, ChxType::NOTE_3D);
//                 cdl.setValueOf(KEY_2091, index());
//                 cdl.setValueOf(KEY_2900, "P M I ! ! !");
//                 ret_ok = true; // ...do it at the end, than we sure there will be no exception anymore...
//                 /// TODO (SS): ...
//
//                 if (ret_ok)
//                     ret_ok = getLocationInfo(&cdl);
//
//                 if (ret_ok)
//                     ret_ok = data_table->append(cdl);
//
//                 if (!ret_ok)
//                     data_table->clear();
//
//             } else {
//                 log << "The objects passed in are invalid.";
//             }
//
//             /* ------------------------------------------------------ */
//
//         } else {
//             log << "The object is not fully initialized.";
//         }
//     } catch (NXOpen::NXException e) {
//         log.warning("*** NXOpen::NXException: ") << e.Message() << debugInfo();
//     } catch (...) {
//         log.warning("*** Exception during an invalid Function call!");
//     }
//
//     return ret_ok;
// }


// vim:ts=8:sw=4:cindent
