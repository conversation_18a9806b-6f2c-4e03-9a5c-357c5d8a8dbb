# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.31

# This file contains all the rules used to get the outputs files
# built from the input files.
# It is included in the main 'build.ninja'.

# =============================================================================
# Project: tw_demo
# Configurations: Debug
# =============================================================================
# =============================================================================

#############################################
# localized /showIncludes string

msvc_deps_prefix = Note: including file: 


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__tw_demo_unscanned_Debug
  deps = msvc
  command = ${LAUNCHER}${CODE_CHECK}C:\PROGRA~1\MICROS~2\2022\PROFES~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe  /nologo /TP $DEFINES $INCLUDES $FLAGS /showIncludes /Fo$out /Fd$TARGET_COMPILE_PDB /FS -c $in
  description = Building CXX object $out


#############################################
# Rule for linking CXX shared library.

rule CXX_SHARED_LIBRARY_LINKER__tw_demo_Debug
  command = C:\WINDOWS\system32\cmd.exe /C "$PRE_LINK && "C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E vs_link_dll --msvc-ver=1944 --intdir=$OBJECT_DIR --rc=C:\PROGRA~2\WI3CF2~1\10\bin\100261~1.0\x64\rc.exe --mt=C:\PROGRA~2\WI3CF2~1\10\bin\100261~1.0\x64\mt.exe --manifests $MANIFESTS -- C:\PROGRA~1\MICROS~2\2022\PROFES~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\link.exe /nologo @$RSP_FILE  /out:$TARGET_FILE /implib:$TARGET_IMPLIB /pdb:$TARGET_PDB /dll /version:0.0 $LINK_FLAGS && $POST_BUILD"
  description = Linking CXX shared library $TARGET_FILE
  rspfile = $RSP_FILE
  rspfile_content = $in_newline $LINK_PATH $LINK_LIBRARIES
  restat = $RESTAT


#############################################
# Rule for running custom commands.

rule CUSTOM_COMMAND
  command = $COMMAND
  description = $DESC


#############################################
# Rule for re-running cmake.

rule RERUN_CMAKE
  command = "C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" --regenerate-during-build -SD:\dev\2025\tw_demo -BD:\dev\2025\tw_demo\out\build\debug
  description = Re-running CMake...
  generator = 1


#############################################
# Rule for cleaning additional files.

rule CLEAN_ADDITIONAL
  command = "C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -DCONFIG=$CONFIG -P CMakeFiles\clean_additional.cmake
  description = Cleaning additional files...


#############################################
# Rule for cleaning all built files.

rule CLEAN
  command = "C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\Ninja\ninja.exe" $FILE_ARG -t clean $TARGETS
  description = Cleaning all built files...


#############################################
# Rule for printing all primary targets available.

rule HELP
  command = "C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\Ninja\ninja.exe" -t targets
  description = All primary targets available:

