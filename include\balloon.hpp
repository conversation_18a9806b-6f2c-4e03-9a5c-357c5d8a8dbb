#pragma once

#include "common_header.hpp"

struct Point {
    double x;
    double y;
    double z;
};

// Abstract class for Balloon (for 2d and 3d balloon)
class Balloon {
	// Balloon has many properties
    int   id;
    int   color;
    Point position;
    int   fontSize;
    int   fontColor;
    int   backgroundColor;
    int   textColor;
    int   borderColor;
    int   shape;

};


void CreateBalloonPmiNote(NXOpen::Point3d point1);
