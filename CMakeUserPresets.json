{"version": 3, "configurePresets": [{"name": "Qt-Debug", "inherits": "Qt-<PERSON><PERSON><PERSON>", "binaryDir": "${sourceDir}/out/build/debug", "cacheVariables": {"CMAKE_BUILD_TYPE": "Debug", "CMAKE_CXX_FLAGS": "-DQT_QML_DEBUG"}, "environment": {"QML_DEBUG_ARGS": "-qmljsdebugger=file:{73662392-73c3-4113-aacc-4c0ebfebebbc},block"}}, {"name": "Qt-Release", "inherits": "Qt-<PERSON><PERSON><PERSON>", "binaryDir": "${sourceDir}/out/build/release", "cacheVariables": {"CMAKE_BUILD_TYPE": "Release"}}, {"hidden": true, "name": "Qt-<PERSON><PERSON><PERSON>", "inherits": "6.9.0_msvc2022_64", "vendor": {"qt-project.org/Default": {"checksum": "p2g8qSfRuZwrHdoqsydD/azGQjs="}}}, {"hidden": true, "name": "6.9.0_msvc2022_64", "inherits": "Qt", "environment": {"QTDIR": "D:/Qt/6.9.0/msvc2022_64"}, "architecture": {"strategy": "external", "value": "x64"}, "generator": "Ninja", "vendor": {"qt-project.org/Version": {"checksum": "sBywmcWZZXChcZz0AVGlSBNHGws="}}}], "vendor": {"qt-project.org/Presets": {"checksum": "fGWdllX5ldLQaSzF+GwGQMzdzSw="}}}