{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "tw_demo", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "tw_demo::@6890427a1f51a3e7e1df", "jsonFile": "target-tw_demo-Debug-ebb7563016899a7fc6ae.json", "name": "tw_demo", "projectIndex": 0}, {"directoryIndex": 0, "id": "tw_demo_autogen::@6890427a1f51a3e7e1df", "jsonFile": "target-tw_demo_autogen-Debug-ae7adbf3d3f240b711c3.json", "name": "tw_demo_autogen", "projectIndex": 0}, {"directoryIndex": 0, "id": "tw_demo_autogen_timestamp_deps::@6890427a1f51a3e7e1df", "jsonFile": "target-tw_demo_autogen_timestamp_deps-Debug-d36da9a19a1fb797e6a4.json", "name": "tw_demo_autogen_timestamp_deps", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "D:/dev/2025/tw_demo/out/build/debug", "source": "D:/dev/2025/tw_demo"}, "version": {"major": 2, "minor": 8}}