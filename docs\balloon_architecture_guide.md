# Balloon Architecture Guide

## Overview

The balloon system has been completely refactored to implement modern design patterns:

1. **Pure Virtual Base Class**: `IBalloon` - Interface for all balloon types
2. **Concrete Implementation**: `PmiBalloon` - Implements PMI balloon functionality  
3. **Factory Pattern**: `BalloonFactory` - Creates appropriate balloon instances
4. **Builder Pattern**: `BalloonBuilder` - Customizes balloon properties

## Architecture Diagram

```
IBalloon (Pure Virtual Interface)
    ↑
PmiBalloon (Concrete Implementation)
    ↑
BalloonFactory (Creates instances)
    ↑
BalloonBuilder (Configures properties)
```

## Key Components

### 1. IBalloon Interface

Pure virtual base class defining the contract for all balloon types:

```cpp
class IBalloon {
public:
    virtual ~IBalloon() = default;
    
    // Core functionality
    virtual NXOpen::NXObject* create() = 0;
    virtual bool update() = 0;
    virtual bool destroy() = 0;
    
    // Property management
    virtual void setProperties(const BalloonProperties& props) = 0;
    virtual BalloonProperties getProperties() const = 0;
    
    // Position and text management
    virtual void setPosition(const Point3D& position) = 0;
    virtual void setText(const std::string& text) = 0;
    
    // Validation and type info
    virtual bool isValid() const = 0;
    virtual BalloonType getType() const = 0;
};
```

### 2. PmiBalloon Implementation

Concrete implementation for PMI balloons with NX-specific functionality:

- Handles NX balloon note creation and management
- Supports attachment to dimensions and FCFs
- Manages leaders, colors, and text properties
- Thread-safe property updates

### 3. BalloonFactory (Factory Pattern)

Creates balloon instances with various convenience methods:

```cpp
// Basic factory methods
auto balloon = BalloonFactory::createPmiBalloon();
auto balloon = BalloonFactory::createIdSymbol();

// Factory with builder
auto balloon = BalloonFactory::createBalloon(BalloonType::PMI_BALLOON, builder);

// Convenience methods
auto balloon = BalloonFactory::createForDimension(dimension, balloonId);
auto balloon = BalloonFactory::createAtPosition(Point3D(100, 100, 0), "Text");
```

### 4. BalloonBuilder (Builder Pattern)

Fluent interface for configuring balloon properties:

```cpp
auto balloon = BalloonFactory::createBalloon(
    BalloonType::PMI_BALLOON,
    BalloonBuilder()
        .setId(100)
        .setText("Custom Balloon")
        .setPosition(150, 150, 0)
        .setSize(18.0)
        .setTextColor("Yellow")
        .setBorderColor("Red")
        .useHighlightSettings()
        .addAdditionalText("Additional Info")
);
```

## Usage Examples

### Example 1: Simple Balloon Creation

```cpp
// Create a simple balloon at a specific position
auto balloon = BalloonFactory::createAtPosition(Point3D(100, 100, 0), "Simple Balloon");
if (balloon && balloon->isValid()) {
    auto nxObject = balloon->create();
}
```

### Example 2: Customized Balloon with Builder

```cpp
// Create a highly customized balloon
auto customBalloon = BalloonFactory::createBalloon(
    BalloonType::PMI_BALLOON,
    BalloonBuilder()
        .setId(200)
        .setText("Custom Balloon")
        .setPosition(150, 150, 0)
        .setSize(18.0)
        .setTextColor("Yellow")
        .setBorderColor("Red")
        .setHasLeader(true)
        .setArrowType(ArrowType::FILLED_ARROW)
        .setStubSize(7.0)
        .addAdditionalText("Built with Builder Pattern")
);

if (customBalloon && customBalloon->isValid()) {
    customBalloon->create();
}
```

### Example 3: Balloon for Dimension

```cpp
// Create balloon attached to a dimension
NXOpen::Annotations::Dimension* dimension = /* get dimension */;
auto balloon = BalloonFactory::createForDimension(dimension, 300);
if (balloon) {
    balloon->create();
}
```

### Example 4: Using Preset Configurations

```cpp
// PMI Balloon with default settings
auto pmiBalloon = BalloonFactory::createBalloon(
    BalloonType::PMI_BALLOON,
    BalloonBuilder()
        .setId(400)
        .setText("PMI-400")
        .setPosition(200, 100, 0)
        .useDefaultPmiSettings()
);

// ID Symbol balloon
auto idSymbol = BalloonFactory::createBalloon(
    BalloonType::ID_SYMBOL,
    BalloonBuilder()
        .setId(500)
        .setText("ID-500")
        .setPosition(250, 100, 0)
        .useIdSymbolSettings()
);

// Highlighted balloon
auto highlighted = BalloonFactory::createBalloon(
    BalloonType::PMI_BALLOON,
    BalloonBuilder()
        .setId(600)
        .setText("IMPORTANT")
        .setPosition(300, 100, 0)
        .useHighlightSettings()
);
```

## Preset Configurations

### Default PMI Settings
- Category: "User Defined"
- Red text, blue border
- Size: 15.0
- Has leader with filled arrow
- Stub size: 5.0

### ID Symbol Settings  
- Category: "ID Symbol"
- Red text, blue border
- Size: 15.0
- No leader
- Circle shape

### Highlight Settings
- Yellow text, red border, black background
- Size: 18.0
- Has leader with filled arrow
- Emphasizes important balloons

## Benefits of New Architecture

### 1. **Separation of Concerns**
- Interface defines contract
- Implementation handles NX specifics
- Factory manages creation logic
- Builder handles configuration

### 2. **Extensibility**
- Easy to add new balloon types
- New implementations can be added without changing existing code
- Builder pattern allows for complex configurations

### 3. **Type Safety**
- Strong typing with enums
- Compile-time checking of balloon types
- Clear API contracts

### 4. **Maintainability**
- Single responsibility principle
- Clear separation between creation and configuration
- Easy to test individual components

### 5. **Flexibility**
- Multiple ways to create balloons
- Preset configurations for common scenarios
- Fluent interface for custom configurations

## Migration from Legacy Code

### Old Way:
```cpp
CreateBalloonPmiNote(point1);  // Limited customization
```

### New Way:
```cpp
auto balloon = BalloonFactory::createAtPosition(Point3D(point1), "Text");
balloon->create();
```

### Advanced New Way:
```cpp
auto balloon = BalloonFactory::createBalloon(
    BalloonType::PMI_BALLOON,
    BalloonBuilder()
        .setText("Advanced Balloon")
        .setPosition(Point3D(point1))
        .useDefaultPmiSettings()
        .setSize(20.0)
);
balloon->create();
```

## Best Practices

1. **Always check validity**: Use `balloon->isValid()` before creating
2. **Use appropriate factory methods**: Choose the most specific factory method for your use case
3. **Leverage presets**: Use preset configurations as starting points
4. **Handle exceptions**: Wrap balloon creation in try-catch blocks
5. **Store smart pointers**: Use `std::unique_ptr<IBalloon>` for automatic memory management

## Future Extensions

The architecture supports easy extension for:
- New balloon types (GeneralNote, CustomSymbol, etc.)
- Additional properties (fonts, line styles, etc.)
- Different NX versions
- Export/import of balloon configurations
- Batch balloon operations
