{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "nx_demo", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-Debug-472ae2c0b21ec4e3a0bb.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-Debug-c86817cdff5dd482d60a.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 0, "id": "nx_demo::@6890427a1f51a3e7e1df", "jsonFile": "target-nx_demo-Debug-600908c3a826162c62ff.json", "name": "nx_demo", "projectIndex": 0}]}, {"directories": [{"build": ".", "jsonFile": "directory-.-Release-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "Release", "projects": [{"directoryIndexes": [0], "name": "nx_demo", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-Release-472ae2c0b21ec4e3a0bb.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-Release-c86817cdff5dd482d60a.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 0, "id": "nx_demo::@6890427a1f51a3e7e1df", "jsonFile": "target-nx_demo-Release-10133ca46313debfef19.json", "name": "nx_demo", "projectIndex": 0}]}, {"directories": [{"build": ".", "jsonFile": "directory-.-MinSizeRel-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "MinSizeRel", "projects": [{"directoryIndexes": [0], "name": "nx_demo", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-MinSizeRel-472ae2c0b21ec4e3a0bb.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-MinSizeRel-c86817cdff5dd482d60a.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 0, "id": "nx_demo::@6890427a1f51a3e7e1df", "jsonFile": "target-nx_demo-MinSizeRel-627ee250bc597ece404c.json", "name": "nx_demo", "projectIndex": 0}]}, {"directories": [{"build": ".", "jsonFile": "directory-.-RelWithDebInfo-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "RelWithDebInfo", "projects": [{"directoryIndexes": [0], "name": "nx_demo", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-472ae2c0b21ec4e3a0bb.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-RelWithDebInfo-c86817cdff5dd482d60a.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 0, "id": "nx_demo::@6890427a1f51a3e7e1df", "jsonFile": "target-nx_demo-RelWithDebInfo-61240315f008dba27fde.json", "name": "nx_demo", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "D:/dev/2025/tw_demo/build", "source": "D:/dev/2025/tw_demo"}, "version": {"major": 2, "minor": 8}}