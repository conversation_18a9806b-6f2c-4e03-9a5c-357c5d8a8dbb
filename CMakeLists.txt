cmake_minimum_required(VERSION 3.18)
project(tw_demo)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)

find_package(QT NAMES Qt6 REQUIRED COMPONENTS Core Quick HINTS D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6)
find_package(Qt${QT_VERSION_MAJOR} REQUIRED COMPONENTS Core Quick HINTS D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6)
find_package(Qt6 COMPONENTS Core Widgets GUI REQUIRED HINTS "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6")


add_library(tw_demo SHARED)

set(UGOPEN_PATH "D:/Program Files/Siemens/NX2306/UGOPEN")
target_include_directories(tw_demo 
    PRIVATE
    ${UGOPEN_PATH}
    include
)

target_sources(tw_demo
    PRIVATE
        src/main.cpp
        src/common_header.cpp
        src/balloon.cpp
        src/qt_singleton_window.cpp
        include/qt_singleton_window.h
        )

target_link_libraries(tw_demo
    ${UGOPEN_PATH}/libameopencpp.lib
    ${UGOPEN_PATH}/libanimationdesigneropencpp.lib
    ${UGOPEN_PATH}/libdmuopencpp.lib
    ${UGOPEN_PATH}/liblinedesigneropencpp.lib
    ${UGOPEN_PATH}/libmfgmlpopencpp.lib
    ${UGOPEN_PATH}/libnxopencpp.lib
    ${UGOPEN_PATH}/libnxopencpp_annotations.lib
    ${UGOPEN_PATH}/libnxopencpp_assemblies.lib
    ${UGOPEN_PATH}/libnxopencpp_bodydes.lib
    ${UGOPEN_PATH}/libnxopencpp_cae.lib
    ${UGOPEN_PATH}/libnxopencpp_cam.lib
    ${UGOPEN_PATH}/libnxopencpp_diagramming.lib
    ${UGOPEN_PATH}/libnxopencpp_diagramminglibraryauthor.lib
    ${UGOPEN_PATH}/libnxopencpp_die.lib
    ${UGOPEN_PATH}/libnxopencpp_display.lib
    ${UGOPEN_PATH}/libnxopencpp_drafting.lib
    ${UGOPEN_PATH}/libnxopencpp_drawings.lib
    ${UGOPEN_PATH}/libnxopencpp_facet.lib
    ${UGOPEN_PATH}/libnxopencpp_features.lib
    ${UGOPEN_PATH}/libnxopencpp_fields.lib
    ${UGOPEN_PATH}/libnxopencpp_formboard.lib
    ${UGOPEN_PATH}/libnxopencpp_gateway.lib
    ${UGOPEN_PATH}/libnxopencpp_geometricanalysis.lib
    ${UGOPEN_PATH}/libnxopencpp_geometricutilities.lib
    ${UGOPEN_PATH}/libnxopencpp_issue.lib
    ${UGOPEN_PATH}/libnxopencpp_layer.lib
    ${UGOPEN_PATH}/libnxopencpp_layout2d.lib
    ${UGOPEN_PATH}/libnxopencpp_markup.lib
    ${UGOPEN_PATH}/libnxopencpp_mechanicalrouting.lib
    ${UGOPEN_PATH}/libnxopencpp_mechatronics.lib
    ${UGOPEN_PATH}/libnxopencpp_modldirect.lib
    ${UGOPEN_PATH}/libnxopencpp_motion.lib
    ${UGOPEN_PATH}/libnxopencpp_openxml.lib
    ${UGOPEN_PATH}/libnxopencpp_optimization.lib
    ${UGOPEN_PATH}/libnxopencpp_options.lib
    ${UGOPEN_PATH}/libnxopencpp_partfamily.lib
    ${UGOPEN_PATH}/libnxopencpp_pdm.lib
    ${UGOPEN_PATH}/libnxopencpp_physmat.lib
    ${UGOPEN_PATH}/libnxopencpp_placement.lib
    ${UGOPEN_PATH}/libnxopencpp_plas.lib
    ${UGOPEN_PATH}/libnxopencpp_positioning.lib
    ${UGOPEN_PATH}/libnxopencpp_preferences.lib
    ${UGOPEN_PATH}/libnxopencpp_report.lib
    ${UGOPEN_PATH}/libnxopencpp_routing.lib
    ${UGOPEN_PATH}/libnxopencpp_shapesearch.lib
    ${UGOPEN_PATH}/libnxopencpp_sheetmetal.lib
    ${UGOPEN_PATH}/libnxopencpp_shipdesign.lib
    ${UGOPEN_PATH}/libnxopencpp_sim.lib
    ${UGOPEN_PATH}/libnxopencpp_tooling.lib
    ${UGOPEN_PATH}/libnxopencpp_userdefinedobjects.lib
    ${UGOPEN_PATH}/libnxopencpp_userdefinedtemplate.lib
    ${UGOPEN_PATH}/libnxopencpp_validate.lib
    ${UGOPEN_PATH}/libnxopencpp_visualreporting.lib
    ${UGOPEN_PATH}/libnxopencpp_weld.lib
    ${UGOPEN_PATH}/libnxopenjava_markup.lib
    ${UGOPEN_PATH}/libnxopenuicpp.lib
    ${UGOPEN_PATH}/libopenintpp.lib
    ${UGOPEN_PATH}/libopenpp.lib
    ${UGOPEN_PATH}/libufun.lib
    ${UGOPEN_PATH}/libufun_cae.lib
    ${UGOPEN_PATH}/libufun_cam.lib
    ${UGOPEN_PATH}/libufun_die.lib
    ${UGOPEN_PATH}/libufun_vdac.lib
    ${UGOPEN_PATH}/libufun_weld.lib
    ${UGOPEN_PATH}/libugopenint.lib
    ${UGOPEN_PATH}/libugopenint_cae.lib
    ${UGOPEN_PATH}/libugopenint_cam.lib
    ${UGOPEN_PATH}/libvmathpp.lib
    Qt6::Widgets
#    Qt${QT_VERSION_MAJOR}::Core 
#    Qt${QT_VERSION_MAJOR}::Quick
)