{"artifacts": [{"path": "Debug/tw_demo.dll"}, {"path": "Debug/tw_demo.lib"}, {"path": "Debug/tw_demo.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "set_target_properties", "include", "find_package", "find_dependency", "_qt_internal_find_qt_dependencies", "target_include_directories", "target_sources"], "files": ["CMakeLists.txt", "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6Widgets/Qt6WidgetsTargets.cmake", "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6Widgets/Qt6WidgetsConfig.cmake", "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6/Qt6Config.cmake", "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreTargets.cmake", "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreConfig.cmake", "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6GuiTargets.cmake", "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6GuiConfig.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake", "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake", "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6Quick/Qt6QuickDependencies.cmake", "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6Quick/Qt6QuickConfig.cmake"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 14, "parent": 0}, {"command": 1, "file": 0, "line": 30, "parent": 0}, {"command": 4, "file": 0, "line": 11, "parent": 0}, {"file": 3, "parent": 3}, {"command": 4, "file": 3, "line": 212, "parent": 4}, {"file": 2, "parent": 5}, {"command": 3, "file": 2, "line": 55, "parent": 6}, {"file": 1, "parent": 7}, {"command": 2, "file": 1, "line": 61, "parent": 8}, {"command": 4, "file": 0, "line": 10, "parent": 0}, {"file": 3, "parent": 10}, {"command": 4, "file": 3, "line": 212, "parent": 11}, {"file": 5, "parent": 12}, {"command": 3, "file": 5, "line": 57, "parent": 13}, {"file": 4, "parent": 14}, {"command": 2, "file": 4, "line": 61, "parent": 15}, {"command": 4, "file": 3, "line": 212, "parent": 11}, {"file": 11, "parent": 17}, {"command": 3, "file": 11, "line": 43, "parent": 18}, {"file": 10, "parent": 19}, {"command": 6, "file": 10, "line": 45, "parent": 20}, {"command": 5, "file": 9, "line": 137, "parent": 21}, {"command": 4, "file": 8, "line": 78, "parent": 22}, {"file": 7, "parent": 23}, {"command": 3, "file": 7, "line": 55, "parent": 24}, {"file": 6, "parent": 25}, {"command": 2, "file": 6, "line": 61, "parent": 26}, {"command": 7, "file": 0, "line": 17, "parent": 0}, {"command": 8, "file": 0, "line": 23, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd"}, {"backtrace": 2, "fragment": "-Zc:__cplusplus"}, {"backtrace": 2, "fragment": "-permissive-"}, {"backtrace": 2, "fragment": "-utf-8"}], "defines": [{"backtrace": 2, "define": "QT_CORE_LIB"}, {"backtrace": 2, "define": "QT_GUI_LIB"}, {"backtrace": 2, "define": "QT_WIDGETS_LIB"}, {"backtrace": 2, "define": "UNICODE"}, {"backtrace": 2, "define": "WIN32"}, {"backtrace": 2, "define": "WIN64"}, {"backtrace": 2, "define": "_ENABLE_EXTENDED_ALIGNED_STORAGE"}, {"backtrace": 2, "define": "_UNICODE"}, {"backtrace": 2, "define": "_WIN64"}, {"define": "tw_demo_EXPORTS"}], "includes": [{"backtrace": 0, "path": "D:/dev/2025/tw_demo/build/tw_demo_autogen/include_Debug"}, {"backtrace": 28, "path": "D:/Program Files/Siemens/NX2306/UGOPEN"}, {"backtrace": 28, "path": "D:/dev/2025/tw_demo/include"}, {"backtrace": 2, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtWidgets"}, {"backtrace": 2, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include"}, {"backtrace": 2, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtCore"}, {"backtrace": 2, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/mkspecs/win32-msvc"}, {"backtrace": 2, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtGui"}], "language": "CXX", "languageStandard": {"backtraces": [2, 2], "standard": "17"}, "sourceIndexes": [0, 1, 2, 3]}], "dependencies": [{"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "id": "tw_demo::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "/machine:x64 /debug /INCREMENTAL", "role": "flags"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libameopencpp.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libanimationdesigneropencpp.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libdmuopencpp.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\liblinedesigneropencpp.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libmfgmlpopencpp.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libnxopencpp.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libnxopencpp_annotations.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libnxopencpp_assemblies.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libnxopencpp_bodydes.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libnxopencpp_cae.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libnxopencpp_cam.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libnxopencpp_diagramming.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libnxopencpp_diagramminglibraryauthor.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libnxopencpp_die.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libnxopencpp_display.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libnxopencpp_drafting.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libnxopencpp_drawings.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libnxopencpp_facet.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libnxopencpp_features.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libnxopencpp_fields.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libnxopencpp_formboard.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libnxopencpp_gateway.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libnxopencpp_geometricanalysis.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libnxopencpp_geometricutilities.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libnxopencpp_issue.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libnxopencpp_layer.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libnxopencpp_layout2d.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libnxopencpp_markup.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libnxopencpp_mechanicalrouting.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libnxopencpp_mechatronics.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libnxopencpp_modldirect.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libnxopencpp_motion.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libnxopencpp_openxml.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libnxopencpp_optimization.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libnxopencpp_options.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libnxopencpp_partfamily.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libnxopencpp_pdm.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libnxopencpp_physmat.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libnxopencpp_placement.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libnxopencpp_plas.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libnxopencpp_positioning.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libnxopencpp_preferences.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libnxopencpp_report.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libnxopencpp_routing.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libnxopencpp_shapesearch.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libnxopencpp_sheetmetal.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libnxopencpp_shipdesign.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libnxopencpp_sim.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libnxopencpp_tooling.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libnxopencpp_userdefinedobjects.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libnxopencpp_userdefinedtemplate.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libnxopencpp_validate.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libnxopencpp_visualreporting.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libnxopencpp_weld.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libnxopenjava_markup.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libnxopenuicpp.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libopenintpp.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libopenpp.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libufun.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libufun_cae.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libufun_cam.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libufun_die.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libufun_vdac.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libufun_weld.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libugopenint.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libugopenint_cae.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libugopenint_cam.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\Program Files\\Siemens\\NX2306\\UGOPEN\\libvmathpp.lib\"", "role": "libraries"}, {"backtrace": 2, "fragment": "D:\\Qt\\6.9.0\\msvc2022_64\\lib\\Qt6Widgetsd.lib", "role": "libraries"}, {"backtrace": 9, "fragment": "D:\\Qt\\6.9.0\\msvc2022_64\\lib\\Qt6Guid.lib", "role": "libraries"}, {"backtrace": 9, "fragment": "D:\\Qt\\6.9.0\\msvc2022_64\\lib\\Qt6Cored.lib", "role": "libraries"}, {"backtrace": 16, "fragment": "mpr.lib", "role": "libraries"}, {"backtrace": 16, "fragment": "userenv.lib", "role": "libraries"}, {"backtrace": 27, "fragment": "d3d11.lib", "role": "libraries"}, {"backtrace": 27, "fragment": "dxgi.lib", "role": "libraries"}, {"backtrace": 27, "fragment": "dxguid.lib", "role": "libraries"}, {"backtrace": 27, "fragment": "d3d12.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "tw_demo", "nameOnDisk": "tw_demo.dll", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/tw_demo_autogen/mocs_compilation_Debug.cpp", "sourceGroupIndex": 0}, {"backtrace": 29, "compileGroupIndex": 0, "path": "src/main.cpp", "sourceGroupIndex": 0}, {"backtrace": 29, "compileGroupIndex": 0, "path": "src/common_header.cpp", "sourceGroupIndex": 0}, {"backtrace": 29, "compileGroupIndex": 0, "path": "src/balloon.cpp", "sourceGroupIndex": 0}], "type": "SHARED_LIBRARY"}