﻿// NX 12.0.1.7
// Journal created by slibc on Sat Feb 20 12:20:47 2021 W. Europe Standard Time
//
#include "NXsigningresource.cpp"

#include <NXOpen/Annotations_Annotation.hxx>
#include <NXOpen/Annotations_Datum.hxx>
#include <NXOpen/Annotations_Dimension.hxx>
#include <NXOpen/Annotations_DimensionCollection.hxx>
#include <NXOpen/Annotations_Fcf.hxx>
#include <NXOpen/Annotations_Pmi.hxx>
#include <NXOpen/Annotations_PmiArcLengthDimension.hxx>
#include <NXOpen/Annotations_PmiAttribute.hxx>
#include <NXOpen/Annotations_PmiBaselineDimension.hxx>
#include <NXOpen/Annotations_PmiChainDimension.hxx>
#include <NXOpen/Annotations_PmiChamferDimension.hxx>
#include <NXOpen/Annotations_PmiCollection.hxx>
#include <NXOpen/Annotations_PmiConcentricCircleDimension.hxx>
#include <NXOpen/Annotations_PmiCustomSymbol.hxx>
#include <NXOpen/Annotations_PmiCylindricalDimension.hxx>
#include <NXOpen/Annotations_PmiDiameterDimension.hxx>
#include <NXOpen/Annotations_PmiFoldedRadiusDimension.hxx>
#include <NXOpen/Annotations_PmiHoleDimension.hxx>
#include <NXOpen/Annotations_PmiHorizontalDimension.hxx>
#include <NXOpen/Annotations_PmiHorizontalOrdinateDimension.hxx>
#include <NXOpen/Annotations_PmiLineWeld.hxx>
#include <NXOpen/Annotations_PmiMajorAngularDimension.hxx>
#include <NXOpen/Annotations_PmiManager.hxx>
#include <NXOpen/Annotations_PmiMinorAngularDimension.hxx>
#include <NXOpen/Annotations_PmiNote.hxx>
#include <NXOpen/Annotations_PmiOrdinateOriginDimension.hxx>
#include <NXOpen/Annotations_PmiParallelDimension.hxx>
#include <NXOpen/Annotations_PmiPerpendicularDimension.hxx>
#include <NXOpen/Annotations_PmiRadiusDimension.hxx>
#include <NXOpen/Annotations_PmiVerticalDimension.hxx>
#include <NXOpen/Annotations_PmiVerticalOrdinateDimension.hxx>
#include <NXOpen/Annotations_PointTarget.hxx>
#include <NXOpen/Axis.hxx>
#include <NXOpen/BasePart.hxx>
#include <NXOpen/Body.hxx>
#include <NXOpen/Builder.hxx>
#include <NXOpen/CoordinateSystemCollection.hxx>
#include <NXOpen/Direction.hxx>
#include <NXOpen/DisplayableObject.hxx>
#include <NXOpen/Drawings_DraftingDrawingSheet.hxx>
#include <NXOpen/Drawings_DraftingDrawingSheetCollection.hxx>
#include <NXOpen/Drawings_DrawingSheetCollection.hxx>
#include <NXOpen/Expression.hxx>
#include <NXOpen/ExpressionCollection.hxx>
#include <NXOpen/Features_AssociativeArc.hxx>
#include <NXOpen/Features_AssociativeArcBuilder.hxx>
#include <NXOpen/Features_BaseFeatureCollection.hxx>
#include <NXOpen/Features_CylinderBuilder.hxx>
#include <NXOpen/Features_FeatureCollection.hxx>
#include <NXOpen/GeometricUtilities_BooleanOperation.hxx>
#include <NXOpen/ICurve.hxx>
#include <NXOpen/ListingWindow.hxx>
#include <NXOpen/NXException.hxx>
#include <NXOpen/NXObject.hxx>
#include <NXOpen/NXObjectManager.hxx>
#include <NXOpen/Part.hxx>
#include <NXOpen/PartCollection.hxx>
#include <NXOpen/Point.hxx>
#include <NXOpen/PointCollection.hxx>
#include <NXOpen/SelectICurve.hxx>
#include <NXOpen/SelectObject.hxx>
#include <NXOpen/SelectPoint.hxx>
#include <NXOpen/Session.hxx>
#include <NXOpen/TaggedObject.hxx>
#include <NXOpen/UI.hxx>
#include <NXOpen/Unit.hxx>
#include <NXOpen/View.hxx>
#include <NXOpen/WCS.hxx>
#include <NXOpen/Xform.hxx>
#include <NXOpen/XformCollection.hxx>
#include <string>
#include <uf.h>
#include <uf_defs.h>
#include <uf_exit.h>
#include <uf_part.h>
#include <uf_ui.h>

#include <NXOpen/Features_Chamfer.hxx>
#include <NXOpen/Features_LabelChamfer.hxx>
#include <NXOpen/Annotations_AnnotationManager.hxx>
#include <NXOpen/Annotations_PmiChamferDimensionBuilder.hxx>
//#include <NXOpen/Features_ApexRangeChamfer.hxx>
//#include <NXOpen/Features_ResizeChamfer.hxx>

// We are currently testing removal of using namespace NXOpen.
// Uncomment the below line if your journal does not compile.
 using namespace NXOpen;

//#include <QApplication>
//#include <QDebug>
//#include <QException>
//#include <QLabel>
//#include <QMainWindow>
//#include <QMessageBox>
//#include <QPushButton>
//#include <io.h>

//#include <WinUser.h>
#if 1
void DrawCircle(NXOpen::Point3d centerPoint, double radius) {
    auto*                             origin1                     = NXOpen::Session::GetSession()->Parts()->Work()->Points()->CreatePoint(centerPoint);
    NXOpen::Features::AssociativeArc* nullFeatures_AssociativeArc = nullptr;
    auto*                             associativeArcBuilder1      = NXOpen::Session::GetSession()->Parts()->Work()->BaseFeatures()->CreateAssociativeArcBuilder(nullFeatures_AssociativeArc);
    associativeArcBuilder1->SetType(NXOpen::Features::AssociativeArcBuilder::TypesArcFromCenter);
    associativeArcBuilder1->Limits()->SetFullCircle(true);
    associativeArcBuilder1->CenterPoint()->SetValue(origin1);
    associativeArcBuilder1->SetEndPointOptions(NXOpen::Features::AssociativeArcBuilder::EndOptionRadius);
    associativeArcBuilder1->Radius()->SetRightHandSide(std::to_string(radius));
    associativeArcBuilder1->SetAssociative(false);

    auto* nXObject1 = associativeArcBuilder1->Commit();
    auto  objects1  = associativeArcBuilder1->GetCommittedObjects();
    associativeArcBuilder1->Destroy();
}

void motion_cb(double screen_pos[3], UF_UI_motion_cb_data_p_t motion_cb_data, void* data) {
    char str[100];
    sprintf(str, "Mouse at (%4.2f,%4.2f,%4.2f)", screen_pos[0], screen_pos[1], screen_pos[2]);
    uc1601(str, 0);

    NXOpen::Point3d centerPoint {screen_pos[0], screen_pos[1], screen_pos[2]};
    DrawCircle(centerPoint, 20);
}

NXOpen::Point3d DlgTest() {
    double screenPt[3] = {0, 0, 0};
    tag_t  view_tag;
    int    response;
    char   msg[100];
    //UF_UI_specify_screen_position(
    //        "Specify Screen Position:",
    //        &motion_cb,
    //        nullptr,
    //        screenPt,
    //        &view_tag,
    //        &response);
    //if (response == UF_UI_PICK_RESPONSE) {
    //    sprintf(msg, "You Pick Screen Point(%4.2f,%4.2f,%4.2f)", screenPt[0], screenPt[1], screenPt[2]);
    //    uc1601(msg, 1);
    //}
    return {screenPt[0], screenPt[1], screenPt[2]};
}

/*
 * Show a circle of the desired size that moves around with the cursor as the user decides where
 * to place it. When the user clicks to confirm their choice, an actual arc object will be created
 * at the specified position.
 */
void DrawCircles() {
loop:
    auto* theSession = NXOpen::Session::GetSession();
    auto* theUI      = NXOpen::UI::GetUI();
    //auto theUfSession = NXOpen::UF::UFSession->GetUFSession();
    auto* listingWindow = theSession->ListingWindow();

    auto*            workPart      = theSession->Parts()->Work();
    constexpr double inchCircleDia = 2;
    constexpr double mmCircleDia   = 50;

    double circleDia = 0;
    listingWindow->Open();

    if (workPart->PartUnits() == NXOpen::BasePart::Units::UnitsInches)
        circleDia = inchCircleDia;
    else
        circleDia = mmCircleDia;

    NXOpen::NXString undoMarkName = "NXJ overlay graphics demo";
    auto             markId1      = theSession->SetUndoMark(NXOpen::Session::MarkVisibilityVisible, undoMarkName);

    //move the WCS to absolute (to keep our example simple)
    auto* absXform = workPart->Xforms()->CreateXform(NXOpen::SmartObject::UpdateOptionWithinModeling, 1);
    auto* absCsys  = workPart->CoordinateSystems()->CreateCoordinateSystem(absXform, NXOpen::SmartObject::UpdateOptionWithinModeling);
    auto* csys3    = workPart->WCS()->SetCoordinateSystemCartesianAtCsys(absCsys);

    //define the orientation of our circle object to lie on the absolute XY plane
    double circleOrientation[] = {1, 0, 0, 0, 1, 0, 0, 0, 1};
    auto   selectedPoint       = DlgTest();

    DrawCircle(selectedPoint, 30);
    goto loop;
}

/**
 * \brief extract all the dimensions from a 2D drawing
 * Sheet Nr. | Zone | Balloon Nr. | Description\Measurement Direction | Nominal Value | Tolerance Type | Upper Tolerance | Lower Tolerance | Maximum Value | Minimum Value
 */
void GetAllAnnotations() {
    //First read the active part file from NX.
    //Then, loop through the sheets.
    //Then, loop through the dimensions in the sheet
    //Grab the zone, text, tolerance type, tolerance and the measurement direction for each.
    //Grab the nearest annotation in that zone.
    //Build an array or a dictionary (similar to JSON or Python's Dictionary class)
    //Then, loop through the text in the document, and do the same steps, except for those that are inapplicable, of course.
    //Ask for a savefile location.
    //Create an Excel file in that location
    //Open the Excel file.
    //Write array to the excel file.

    auto* theSession = NXOpen::Session::GetSession();
    auto* workPart   = theSession->Parts()->Work();
    auto* dimensions = workPart->Dimensions();
    for (auto it = dimensions->begin(); it != dimensions->end(); ++it) {
        auto* tempDimension           = dynamic_cast<NXOpen::Annotations::Dimension*>(*it);
        auto* currentDrawingSheet     = workPart->DrawingSheets()->CurrentDrawingSheet();
        auto  currentDrawingSheetName = currentDrawingSheet->Name();
        auto  nominalValue            = tempDimension->ComputedSize();
        auto  measurementDirection    = tempDimension->MeasurementDirection();

        double                             lowerMetricToleranceValue = 0.F;
        double                             upperMetricToleranceValue = 0.F;
        double                             maximumValue              = 0.F;
        double                             minimumValue              = 0.F;
        NXOpen::Annotations::ToleranceType toleranceType             = tempDimension->ToleranceType();
        switch (toleranceType) {
        case NXOpen::Annotations::ToleranceType::ToleranceTypeUnilateralBelow:
            lowerMetricToleranceValue = tempDimension->LowerMetricToleranceValue();
            maximumValue              = nominalValue;
            minimumValue              = nominalValue + lowerMetricToleranceValue;
            break;
        case NXOpen::Annotations::ToleranceType::ToleranceTypeUnilateralAbove:
            upperMetricToleranceValue = tempDimension->UpperMetricToleranceValue();
            maximumValue              = nominalValue + upperMetricToleranceValue;
            minimumValue              = nominalValue;
            break;
        case NXOpen::Annotations::ToleranceType::ToleranceTypeLimitsAndFits:
            //lowerMetricToleranceValue = tempDimension->LimitFitDeviation();
            //upperMetricToleranceValue = tempDimension->LimitFitGrade();
            //maximumValue = "As per standards";
            //minimumValue = "As per standards";
            break;
        case NXOpen::Annotations::ToleranceType::ToleranceTypeBilateralOneLine:
            upperMetricToleranceValue = tempDimension->UpperMetricToleranceValue();
            lowerMetricToleranceValue = -1 * upperMetricToleranceValue;
            maximumValue              = nominalValue + upperMetricToleranceValue;
            maximumValue              = nominalValue - upperMetricToleranceValue;
            break;
        case NXOpen::Annotations::ToleranceType::ToleranceTypeBilateralTwoLines:
            upperMetricToleranceValue = tempDimension->UpperMetricToleranceValue();
            lowerMetricToleranceValue = tempDimension->LowerMetricToleranceValue();
            maximumValue              = nominalValue + upperMetricToleranceValue;
            maximumValue              = nominalValue + lowerMetricToleranceValue;
            break;
        case NXOpen::Annotations::ToleranceType::ToleranceTypeNone:
            break;
        case NXOpen::Annotations::ToleranceType::ToleranceTypeBasic:
            break;
        case NXOpen::Annotations::ToleranceType::ToleranceTypeLimitOneLine:
            upperMetricToleranceValue = tempDimension->UpperMetricToleranceValue();
            lowerMetricToleranceValue = tempDimension->LowerMetricToleranceValue();
            maximumValue              = nominalValue + upperMetricToleranceValue;
            maximumValue              = nominalValue + lowerMetricToleranceValue;
            break;
        default:
            break;
        }
    }
}

void temp() {
    tag_t                 pmiTag       = NULL_TAG;
    NXOpen::TaggedObject* pmiTaggedObj = NXOpen::NXObjectManager::Get(pmiTag);

    if (dynamic_cast<NXOpen::Annotations::PmiArcLengthDimension*>(pmiTaggedObj) ||
        dynamic_cast<NXOpen::Annotations::PmiBaselineDimension*>(pmiTaggedObj) ||
        dynamic_cast<NXOpen::Annotations::PmiChainDimension*>(pmiTaggedObj) ||
        dynamic_cast<NXOpen::Annotations::PmiChamferDimension*>(pmiTaggedObj) ||
        dynamic_cast<NXOpen::Annotations::PmiConcentricCircleDimension*>(pmiTaggedObj) ||
        dynamic_cast<NXOpen::Annotations::PmiCylindricalDimension*>(pmiTaggedObj) ||
        dynamic_cast<NXOpen::Annotations::PmiDiameterDimension*>(pmiTaggedObj) ||
        dynamic_cast<NXOpen::Annotations::PmiFoldedRadiusDimension*>(pmiTaggedObj) ||
        dynamic_cast<NXOpen::Annotations::PmiHoleDimension*>(pmiTaggedObj) ||
        dynamic_cast<NXOpen::Annotations::PmiHorizontalDimension*>(pmiTaggedObj) ||
        dynamic_cast<NXOpen::Annotations::PmiHorizontalOrdinateDimension*>(pmiTaggedObj) ||
        dynamic_cast<NXOpen::Annotations::PmiMajorAngularDimension*>(pmiTaggedObj) ||
        dynamic_cast<NXOpen::Annotations::PmiMinorAngularDimension*>(pmiTaggedObj) ||
        dynamic_cast<NXOpen::Annotations::PmiOrdinateOriginDimension*>(pmiTaggedObj) ||
        dynamic_cast<NXOpen::Annotations::PmiParallelDimension*>(pmiTaggedObj) ||
        dynamic_cast<NXOpen::Annotations::PmiPerpendicularDimension*>(pmiTaggedObj) ||
        dynamic_cast<NXOpen::Annotations::PmiRadiusDimension*>(pmiTaggedObj) ||
        dynamic_cast<NXOpen::Annotations::PmiVerticalDimension*>(pmiTaggedObj) ||
        dynamic_cast<NXOpen::Annotations::PmiVerticalOrdinateDimension*>(pmiTaggedObj) ||
        dynamic_cast<NXOpen::Annotations::Fcf*>(pmiTaggedObj) ||
        dynamic_cast<NXOpen::Annotations::Datum*>(pmiTaggedObj) ||
        dynamic_cast<NXOpen::Annotations::PmiCustomSymbol*>(pmiTaggedObj) ||
        dynamic_cast<NXOpen::Annotations::PmiNote*>(pmiTaggedObj) ||
        dynamic_cast<NXOpen::Annotations::PmiAttribute*>(pmiTaggedObj) ||
        dynamic_cast<NXOpen::Annotations::PointTarget*>(pmiTaggedObj) ||
        dynamic_cast<NXOpen::Annotations::PmiLineWeld*>(pmiTaggedObj)) {
        auto* annotation = dynamic_cast<NXOpen::Annotations::Annotation*>(pmiTaggedObj);
    }
}

//class MyException : public QException {
//public:
//    void raise() const override {
//        throw *this;
//    }
//    MyException* clone() const override {
//        return new MyException(*this);
//    }
//};

/**
 * \brief QT GUI should be encapsulated into a singleton.
 */
//struct Data {
//    int          argc    = 1;
//    char*        argv[2] = {"dummy", {}};
//    QApplication app {argc, nullptr};
//    QMainWindow  window;
//} * d;

void startup() {
    //d = new Data;
    ////d->msgBox.show();

    //QLabel* label = new QLabel("Hello NX");
    //label->show();

    //d->window.show();
    //d->app.processEvents();

    auto* theSession = NXOpen::Session::GetSession();
    auto* workPart = theSession->Parts()->Work();
    auto* listingWindow = theSession->ListingWindow();
    //listingWindow->Open();
    //listingWindow->WriteLine(workPart->Name());

    //int          argc = 1;
    //QApplication a(argc, nullptr);
    //MainWindow   w;
    //w.show();

    //return a.exec();
}

class QtApplicationInstance {
    //std::atomic<bool>                    isInitialized = false;
    //std::unique_ptr<QApplication>        app;
    //std::unique_ptr<QMainWindow>         window;
    //inline static QtApplicationInstance* myInstance {nullptr};

    //QtApplicationInstance() {
    //    if (!isInitialized)
    //        init();
    //}

    //void init() {
    //    int   argc    = 1;
    //    char* argv[2] = {"dummy", {}};
    //    app           = std::make_unique<QApplication>(argc, argv);
    //    window        = std::make_unique<QMainWindow>();

    //    window->show();
    //    app->processEvents();
    //}

    //public:
    //    static QtApplicationInstance* GetInstance() {
    //        static QtApplicationInstance* myInstance = nullptr;
    //        if (!myInstance)
    //            myInstance = new QtApplicationInstance;
    //        return myInstance;
    //    }
    //
    //    QtApplicationInstance(QtApplicationInstance const&) = delete;
    //    QtApplicationInstance& operator=(QtApplicationInstance const&) = delete;
};

#include <NXOpen/BlockStyler_DoubleBlock.hxx>
//void QtFunc() {
//    NXOpen::Features::BlockFeatureBuilder* blockFeatureBuilder = nullptr;
//    NXOpen::BlockStyler::DoubleBlock*      db1Length           = nullptr;
//    NXOpen::BlockStyler::DoubleBlock*      db1Width            = nullptr;
//    NXOpen::BlockStyler::DoubleBlock*      db1Height           = nullptr;
//    try {
//        //QStringList lib_paths = QCoreApplication::libraryPaths();
//        //QCoreApplication::setLibraryPaths(lib_paths);
//        //TCHAR filename[256];
//        //GetWindowModuleFileName(/*HWND*/ (HWN)UF_UI_get_default_parent(), /*LPTSTR*/ filename, /*UINT*/ 255);
//        //HMODULE parent_module = GetModuleHandle(filename);
//
//        //QMfcApp::pluginInstance(parent_module);
//
//        //qApp->setQuitOnLastWindowClosed(false);
//        //qApp->setOrganizationDomain("bct-technology.com");
//        //qApp->setOrganizationName("BCT Technology AG");
//
//        //{static_cast<QWidget*>(UF_UI_get_default_parent())};
//        //d->msgBox.setWindowTitle("title");
//        //d->msgBox.setText("Question");
//        //d->msgBox.setStandardButtons(QMessageBox::Yes);
//        //d->msgBox.addButton(QMessageBox::No);
//        //d->msgBox.setDefaultButton(QMessageBox::No);
//        //if (d->msgBox.exec() == QMessageBox::Yes) {
//        //    // do something
//        //}
//        //else {
//        //    // do something else
//        //}
//
//        //auto* qtInstance = QtApplicationInstance::GetInstance();
//        //auto* inst2      = QtApplicationInstance::GetInstance();
//    }
//    catch (MyException& ex) {
//        std::string err = ex.what();
//        int         n   = 100;
//    }
//}

void GetDisplayInstances() {
    auto* theSession    = NXOpen::Session::GetSession();
    auto* workPart      = theSession->Parts()->Work();
    auto* listingWindow = theSession->ListingWindow();
    listingWindow->Open();

    auto* pmiCollection = workPart->PmiManager()->Pmis();
    for (auto it = pmiCollection->begin(); it != pmiCollection->end(); ++it) {
        auto* pmi = dynamic_cast<NXOpen::Annotations::Pmi*>(*it);
        pmi->Name();

        auto displayInstances = pmi->GetDisplayInstances();
        int  count            = 0;
        for (auto const& disp : displayInstances) {
            count++;
        }
        listingWindow->WriteLine(std::to_string(count));
    }
}

void GetAllFeatures() {
    auto* theSession    = NXOpen::Session::GetSession();
    auto* workPart      = theSession->Parts()->Work();
    auto  listingWindow = theSession->ListingWindow();
    listingWindow->Open();

    auto features = workPart->Features();
    int  count    = std::distance(features->begin(), features->end());
    for (auto it = features->begin(); it != features->end(); ++it) {
        auto feature = dynamic_cast<NXOpen::Features::Feature*>(*it);
        auto name    = feature->GetFeatureName();
        listingWindow->WriteLine(name);
    }
}

void GetAllObjectOnDrawingSheets() {
    auto* theSession = NXOpen::Session::GetSession();
    auto* workPart   = theSession->Parts()->Work();

    auto* draftDrawingSheets = workPart->DraftingDrawingSheets();
    for (auto it = draftDrawingSheets->begin(); it != draftDrawingSheets->end(); ++it) {
        auto* drawingSheet   = dynamic_cast<NXOpen::Drawings::DrawingSheet*>(*it);
        auto  view           = drawingSheet->View();
        auto  visibleObjects = view->AskVisibleObjects();
        int   count          = 0;
        theSession->ListingWindow()->Open();
        for (const auto& obj : visibleObjects) {
            if (auto* annotation = dynamic_cast<NXOpen::Annotations::Annotation*>(obj)) {
                if (auto* dimension = dynamic_cast<NXOpen::Annotations::Dimension*>(annotation)) {
                    auto measurementType = dimension->GetMeasurementType();
                    auto nominalValue    = dimension->ComputedSize();

                    theSession->ListingWindow()->WriteLine(std::to_string(nominalValue));
                }
                count++;
            }
        }
        theSession->ListingWindow()->WriteLine("Total: " + std::to_string(count));
    }
}
#endif


#include <QDebug>
#include <QGuiApplication>
#include <QQmlApplicationEngine>

 int CreateQmlWindow(int argc, char* argv[]) {
     //qDebug() << argc << endl;
     //for (int i = 0; i < argc; ++i)
     //    qDebug() << argv[i] << endl;
#if QT_VERSION < QT_VERSION_CHECK(6, 0, 0)
     QCoreApplication::setAttribute(Qt::AA_EnableHighDpiScaling);
#endif
     QGuiApplication app(argc, argv);

     QQmlApplicationEngine engine;
     const QUrl            url(QStringLiteral("qrc:/main.qml"));
     QObject::connect(
             &engine,
             &QQmlApplicationEngine::objectCreated,
             &app,
             [url](QObject* obj, const QUrl& objUrl) {
                 if (!obj && url == objUrl)
                     QCoreApplication::exit(-1);
             },
             Qt::QueuedConnection);
     engine.load(url);

     return app.exec();
 }



extern "C" DllExport int ufusr_ask_unload() {
    return static_cast<int>(NXOpen::Session::LibraryUnloadOptionImmediately);
}

extern "C" DllExport void ufusr(char* param, int* retCode, int paramLen) {
    //startup();
    CreateQmlWindow(1, nullptr);
    //UF_initialize();
    //try {
    //    auto* theSession    = NXOpen::Session::GetSession();
    //    auto* workPart      = theSession->Parts()->Work();
    //    auto  listingWindow = theSession->ListingWindow();
    //    listingWindow->Open();
    //    listingWindow->WriteLine("Hello World");

    //    auto* pmiCollection = workPart->Dimensions();
    //    for (auto it = pmiCollection->begin(); it != pmiCollection->end(); ++it) {
    //        if (auto* pmiChamerDimension = dynamic_cast<NXOpen::Annotations::PmiChamferDimension*>(*it); pmiChamerDimension) {

    //            auto* pmiChamferDimensionBuilder = workPart->Dimensions()->CreatePmiChamferDimensionBuilder(pmiChamerDimension);

    //            //enum ChamferForm
    //            //{
    //            //    ChamferFormSymbol/** C5 */,
    //            //    ChamferFormSize/** 5 x 5 */,
    //            //    ChamferFormSizeAngle/** 5 x 45 */,
    //            //    ChamferFormAngleSize/** 45 x 5 */,
    //            //    ChamferFormLast/** Last enum value. Should not be used. */
    //            //};

    //            auto chamferForm = pmiChamferDimensionBuilder->Style()->DimensionStyle()->ChamferForm();
    //            listingWindow->WriteLine(std::to_string(static_cast<int>(chamferForm)));


    //            auto* associatedObject = pmiChamerDimension->GetAssociatedObject();
    //            auto objects = associatedObject->GetObjects();
    //            for (auto& obj : objects) {
    //                listingWindow->WriteLine("associated object");
    //                
    //                if (auto* fcf = dynamic_cast<NXOpen::Features::LabelChamfer*>(obj)) {
    //                    listingWindow->WriteLine("label chamfer");
    //                }

    //            }



    //            auto  value        = pmiChamerDimension->ComputedSize();
    //            auto* appendedText = pmiChamerDimension->GetAppendedText();

    //            listingWindow->WriteLine(std::to_string(value));
    //            for (const auto text : appendedText->GetAboveText()) {
    //                listingWindow->WriteLine("111");
    //                listingWindow->WriteLine(text);
    //            }

    //            for (auto text : appendedText->GetAfterText()) {
    //                listingWindow->WriteLine("222");
    //                listingWindow->WriteLine(text);
    //            }

    //            for (auto text : appendedText->GetBeforeText()) {
    //                listingWindow->WriteLine("333");
    //                listingWindow->WriteLine(text);
    //            }

    //            for (auto text : appendedText->GetBelowText()) {
    //                listingWindow->WriteLine("444");
    //                listingWindow->WriteLine(text);
    //            }

    //            std::vector<NXOpen::NXString> mainTexts, minorTexts;
    //            pmiChamerDimension->GetDimensionText(mainTexts, minorTexts);
    //            for (auto text : mainTexts) {
    //                //listingWindow->WriteLine("555");
    //                listingWindow->WriteLine(text);
    //            }

    //            for (auto text : minorTexts) {
    //                listingWindow->WriteLine("666");
    //                listingWindow->WriteLine(text);
    //            }
    //        }
    //    }


    //    //GetDisplayInstances();
    //    //GetAllFeatures();
    //    //GetAllObjectOnDrawingSheets();

    //    // if (UF_initialize())
    //    //     return;
    //    // //Add your code here
    //    ////DlgTest();

    //    //DrawCircles();
    //    //
    //    // UF_terminate();
    //    //QtFunc();
    //    //if (!d)
    //    //    startup();
    //}
    //catch (NXOpen::NXException const& ne) {
    //    std::string err = ne.Message();
    //}
    //UF_terminate();
}
