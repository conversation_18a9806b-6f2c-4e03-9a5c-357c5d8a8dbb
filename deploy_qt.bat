@echo off
REM Deploy Qt6 libraries for tw_demo.dll
REM This script deploys Qt6 libraries to the same directory as the DLL

echo Deploying Qt6 libraries for tw_demo.dll...

REM Set Qt6 path
set QT6_PATH=D:\Qt\6.9.0\msvc2022_64\bin

REM Check if Qt6 windeployqt exists
if not exist "%QT6_PATH%\windeployqt.exe" (
    echo Error: windeployqt.exe not found at %QT6_PATH%
    echo Please update the QT6_PATH variable in this script to point to your Qt6 installation
    pause
    exit /b 1
)

REM Deploy Debug version
if exist "build\Debug\tw_demo.dll" (
    echo Deploying Debug version...
    "%QT6_PATH%\windeployqt.exe" --debug --dir "build\Debug" "build\Debug\tw_demo.dll"
    if %ERRORLEVEL% EQU 0 (
        echo Debug deployment successful!
    ) else (
        echo Debug deployment failed!
    )
) else (
    echo Debug version not found: build\Debug\tw_demo.dll
)

REM Deploy Release version
if exist "build\Release\tw_demo.dll" (
    echo Deploying Release version...
    "%QT6_PATH%\windeployqt.exe" --release --dir "build\Release" "build\Release\tw_demo.dll"
    if %ERRORLEVEL% EQU 0 (
        echo Release deployment successful!
    ) else (
        echo Release deployment failed!
    )
) else (
    echo Release version not found: build\Release\tw_demo.dll
)

echo.
echo Qt6 deployment completed!
echo.
echo The following Qt6 libraries should now be available in the same directory as tw_demo.dll:
echo - Qt6Cored.dll / Qt6Core.dll
echo - Qt6Guid.dll / Qt6Gui.dll  
echo - Qt6Widgetsd.dll / Qt6Widgets.dll
echo - Plus various Qt plugins in subdirectories
echo.
echo You can now load tw_demo.dll in NX without Qt library issues.
echo.
pause
