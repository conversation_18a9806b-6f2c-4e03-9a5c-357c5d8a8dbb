#include "balloon.hpp"
#include "common_header.hpp"


#include <NXOpen/Annotations_AnnotationManager.hxx>
#include <NXOpen/Annotations_BalloonNote.hxx>
#include <NXOpen/Annotations_BalloonNoteBuilder.hxx>
#include <NXOpen/Annotations_DimensionCollection.hxx>
#include <NXOpen/Annotations_DraftingFcf.hxx>
#include <NXOpen/Annotations_IdSymbol.hxx>
#include <NXOpen/Annotations_IdSymbolCollection.hxx>
#include <NXOpen/Annotations_MinorAngularDimension.hxx>
#include <NXOpen/Annotations_PmiAttributeCollection.hxx>
#include <NXOpen/Annotations_PmiManager.hxx>
#include <NXOpen/Body.hxx>
#include <NXOpen/BodyCollection.hxx>
#include <NXOpen/ColorManager.hxx>
#include <NXOpen/Curve.hxx>
#include <NXOpen/PartCollection.hxx>
#include <NXOpen/SelectDisplayableObject.hxx>
#include <NXOpen/ModelingViewCollection.hxx>
#include <NXOpen/ModelingView.hxx>
#include <NXOpen/ViewCollection.hxx>
#include <NXOpen/View.hxx>
#include <NXOpen/Information.hxx>
#include <sstream>
#include <uf.h>
#include <uf_obj.h>
#include <NXOpen/ViewCollection.hxx>

// extern "C" DllExport int ufusr_ask_unload() {
//     return static_cast<int>(NXOpen::Session::LibraryUnloadOptionImmediately);
// }

// void Example2Curves(NXOpen::Part* workPart, NXOpen::ListingWindow* lw) {
//     try {
//         auto curves = workPart->Curves();

//         // create a line with two end points -->
//         NXOpen::Point3d p1 {50, -100, 0};
//         NXOpen::Point3d p2 {50, 100, 0};
//         auto            line = curves->CreateLine(p1, p2);
//         // <--

//         for (const auto curve : *curves) {
//             const double length = curve->GetLength();

//             //curve->GetLocations();
//             //if (!curve->GetLocations().empty()) {
//             //    lw->WriteLine("No empty curve");
//             //}
//             //auto locations = curve->GetLocations();
//             //for (const auto point : locations) {
//             //    auto x = point.Location.X;
//             //    auto y = point.Location.Y;
//             //    auto z = point.Location.Z;

//             //    //std::string output;
//             //    //output += "Curve: length = " + std::to_string(length);
//             //    //output += " locations = (" + std::to_string(x) + ", " + std::to_string(y) + ", " + std::to_string(z) + ")";
//             //    //lw->WriteLine(output.c_str());
//             //    lw->WriteLine(std::to_string(x).c_str());
//             //}
//             lw->WriteLine(std::to_string(length).c_str());
//         }
//     }
//     catch (NXOpen::NXException const& ne) {
//         lw->WriteLine(ne.Message());
//     }
// }


// void Example4ReadProperties(NXOpen::Part* workPart, NXOpen::ListingWindow* lw) {
//     auto bodyCollection = workPart->Bodies();
//     for (const auto& body : *bodyCollection) {
//         auto attributeInformation = body->GetUserAttributes();
//         for (const auto& attribute : attributeInformation) {
//             lw->WriteLine(attribute.Title + " " + attribute.StringValue);
//         }
//     }
// }

// void CreateBalloon(double x, double y, double z) {
//     auto* theSession = NXOpen::Session::GetSession();
//     auto* workPart   = theSession->Parts()->Work();

//     NXOpen::Annotations::BalloonNote*        balloonNote        = nullptr;
//     NXOpen::Annotations::BalloonNoteBuilder* balloonNoteBuilder = nullptr;
//     balloonNoteBuilder                                          = workPart->PmiManager()->PmiAttributes()->CreateBalloonNoteBuilder(balloonNote);
//     balloonNoteBuilder->Origin()->SetAnchor(NXOpen::Annotations::OriginBuilder::AlignmentPositionMidCenter);
//     balloonNoteBuilder->Origin()->SetInferRelativeToGeometry(true);
//     balloonNoteBuilder->Origin()->Plane()->SetPlaneMethod(NXOpen::Annotations::PlaneBuilder::PlaneMethodTypeModelView);
//     std::vector<NXOpen::NXString> text {"100"};
//     balloonNoteBuilder->SetText(text);

//     balloonNoteBuilder->SetBalloonText("100");
//     NXOpen::View*   view = nullptr;
//     NXOpen::Point3d point {x, y, z};
//     balloonNoteBuilder->Origin()->Origin()->SetValue(nullptr, view, point);
//     NXOpen::NXObject* nXObject1 = balloonNoteBuilder->Commit();
//     balloonNoteBuilder->Destroy();
// }

#define UF_CALL(X) (ReportError(__FILE__, __LINE__, #X, (X)))
static int ReportError(const char* file, int line, const char* call, int ret) {
    if (ret) {
        char err[255], msg[255];

        sprintf(msg, "*** ERROR code %d at line %d in %s:\n+++ ", ret, line, file);
        UF_get_fail_message(ret, err);

        /*  NOTE:  UF_print_syslog is new in V18 */
        UF_print_syslog(msg, FALSE);
        UF_print_syslog(err, FALSE);
        char newline[] = "\n";
        UF_print_syslog(newline, FALSE);
        UF_print_syslog(const_cast<char*>(call), FALSE);
        char semicolon[] = ";\n";
        UF_print_syslog(semicolon, FALSE);

        if (!UF_UI_open_listing_window()) {
            UF_UI_write_listing_window(msg);
            UF_UI_write_listing_window(err);
            UF_UI_write_listing_window("\n");
            UF_UI_write_listing_window(const_cast<char*>(call));
            UF_UI_write_listing_window(";\n");
        }
    }

    return (ret);
}

#include <fstream>

void CreateBalloonManually() {
    auto*                 theUI            = NXOpen::UI::GetUI();
    auto*                 selectionManager = theUI->SelectionManager();
    NXOpen::TaggedObject* selectedObject;
    NXOpen::Point3d       cursor;
    NXOpen::NXString      cue {"Please select a line to be hidden"};
    NXOpen::NXString      title {"Select lines"};
    auto                  scope     = NXOpen::Selection::SelectionScope::SelectionScopeAnyInAssembly;
    //auto                  action    = NXOpen::Selection::SelectionAction::SelectionActionClearAndEnableSpecific;
    bool                  highlight = false;
    auto                  types     = NXOpen::Selection::SelectionType::SelectionTypeAll;

    auto lw = NXOpen::Session::GetSession()->ListingWindow();
    lw->Open();
    lw->WriteLine("Debug: Starting selection process."); // Test static output

    while (auto response = selectionManager->SelectTaggedObject(cue, title, scope, types, highlight, &selectedObject, &cursor)) {
        if (response == NXOpen::Selection::Response::ResponseCancel || response == NXOpen::Selection::Response::ResponseBack) {
            lw->WriteLine("Debug: Selection canceled or back.");
            break;
        }

        if (!selectedObject) {
            lw->WriteLine("Error: Selected object is null.");
            continue;
        }

        std::stringstream ss;
        ss.imbue(std::locale::classic()); // Ensure consistent string formatting
        tag_t tag  = selectedObject->Tag();
        int   type = -1, subtype = -1;
        int   error = UF_OBJ_ask_type_and_subtype(tag, &type, &subtype);
        if (error != 0) {
            lw->WriteLine("Error: Failed to get type and subtype for tag " + std::to_string(tag));
            continue;
        }

        ss << "type: " << type << " subtype: " << subtype;
        std::string output = ss.str();
        lw->WriteFullline(output.c_str()); // Use WriteFulLine to avoid truncation

        // // Optional: Log to file for verification
        // std::ofstream file("D:\\Temp\\debug_log.txt", std::ios::app);
        // file << output << std::endl;
        // file.close();

        // balloon dimension
        if (auto* dimension = dynamic_cast<NXOpen::Annotations::Dimension*>(NXOpen::NXObjectManager::Get(tag))) {
            lw->WriteLine("Debug: Selected dimension.");
            int dimensionType {-1}, dimensionSubtype {-1};
            UF_CALL(UF_OBJ_ask_type_and_subtype(dimension->Tag(), &dimensionType, &dimensionSubtype));

            NXOpen::Session* theSession = NXOpen::Session::GetSession();
            NXOpen::Part*    workPart(theSession->Parts()->Work());

            NXOpen::Annotations::IdSymbol* idSymbol {nullptr};
            auto*                          idSymbolBuilder = workPart->Annotations()->IdSymbols()->CreateIdSymbolBuilder(idSymbol);

            idSymbolBuilder->Origin()->SetInferRelativeToGeometry(true);

            idSymbolBuilder->Origin()->SetAnchor(NXOpen::Annotations::OriginBuilder::AlignmentPositionMidCenter);

            int balloonId = 100; //GetAvailableBalloonId();
            idSymbolBuilder->SetUpperText(std::to_string(balloonId).c_str());
            idSymbolBuilder->SetSize(15.0);

            double textAngle = 0;

            // linear dimension
            if (std::vector<int> linearDimension {1, 2, 5}; std::find(linearDimension.cbegin(), linearDimension.cend(), dimensionSubtype) != linearDimension.cend()) {
                auto* dimensionBuilder = workPart->Dimensions()->CreateLinearDimensionBuilder(dimension);
                textAngle              = dimensionBuilder->Style()->DimensionStyle()->TextAngle();
            }

            // angular dimension
            else if (dimensionSubtype == 6) {
                auto* dimensionBuilder = workPart->Dimensions()->CreateMinorAngularDimensionBuilder(dynamic_cast<NXOpen::Annotations::MinorAngularDimension*>(dimension));
                textAngle              = dimensionBuilder->Style()->DimensionStyle()->TextAngle();
            }

            // radial
            else if (dimensionSubtype == 11 || dimensionSubtype == 9) {
                auto* dimensionBuilder = workPart->Dimensions()->CreateRadialDimensionBuilder(dimension);
                textAngle              = dimensionBuilder->Style()->DimensionStyle()->TextAngle();
            }

            // thickness
            else if (dimensionSubtype == 12) {
                auto* dimensionBuilder = workPart->Dimensions()->CreateThicknessDimensionBuilder(dynamic_cast<NXOpen::Annotations::BaseConcentricCircleDimension*>(dimension));
                textAngle              = dimensionBuilder->Style()->DimensionStyle()->TextAngle();
            }

            else if (dimensionSubtype == 14 || dimensionSubtype == 15) {
                auto* dimensionBuilder = workPart->Dimensions()->CreateOrdinateDimensionBuilder(dynamic_cast<NXOpen::Annotations::OrdinateDimension*>(dimension));
                textAngle              = dimensionBuilder->Style()->DimensionStyle()->TextAngle();
            }

            // chamfer
            else if (dimensionSubtype == 20) {
                auto* dimensionBuilder = workPart->Dimensions()->CreateChamferDimensionBuilder(dynamic_cast<NXOpen::Annotations::BaseChamferDimension*>(dimension));
                textAngle              = dimensionBuilder->Style()->DimensionStyle()->TextAngle();
            }

            idSymbolBuilder->Style()->LetteringStyle()->SetAngle(textAngle);
            idSymbolBuilder->Style()->LetteringStyle()->SetGeneralTextColor(workPart->Colors()->Find("Red"));
            idSymbolBuilder->Style()->SymbolStyle()->SetIdSymbolColor(workPart->Colors()->Find("Blue"));

            NXOpen::View* view {nullptr};
            auto          balloonPosition = dimension->AnnotationOrigin();
            balloonPosition.X -= 3;
            balloonPosition.Y -= 3;
            idSymbolBuilder->Origin()->Origin()->SetValue(nullptr, view, balloonPosition);

            NXOpen::NXObject* nXObject1 = idSymbolBuilder->Commit();

            idSymbolBuilder->Destroy();
        }

        // balloon fcf
        else if (auto* fcf = dynamic_cast<NXOpen::Annotations::DraftingFcf*>(NXOpen::NXObjectManager::Get(tag))) {
            NXOpen::Session* theSession = NXOpen::Session::GetSession();
            NXOpen::Part*    workPart(theSession->Parts()->Work());

            lw->WriteLine("Debug: Selected fcf.");
            NXOpen::Annotations::IdSymbol* idSymbol {nullptr};
            auto*                          idSymbolBuilder = workPart->Annotations()->IdSymbols()->CreateIdSymbolBuilder(idSymbol);

            idSymbolBuilder->Origin()->SetInferRelativeToGeometry(true);

            idSymbolBuilder->Origin()->SetAnchor(NXOpen::Annotations::OriginBuilder::AlignmentPositionMidCenter);

            int balloonId = 100; //GetAvailableBalloonId();
            idSymbolBuilder->SetUpperText(std::to_string(balloonId));
            idSymbolBuilder->SetSize(15.0);

            double textAngle = 0;

            idSymbolBuilder->Style()->LetteringStyle()->SetAngle(textAngle);
            idSymbolBuilder->Style()->LetteringStyle()->SetGeneralTextColor(workPart->Colors()->Find("Red"));
            idSymbolBuilder->Style()->SymbolStyle()->SetIdSymbolColor(workPart->Colors()->Find("Blue"));

            NXOpen::View* view {nullptr};
            auto          balloonPosition = fcf->AnnotationOrigin();
            balloonPosition.X -= 3;
            balloonPosition.Y -= 3;
            idSymbolBuilder->Origin()->Origin()->SetValue(nullptr, view, balloonPosition);

            NXOpen::NXObject* nXObject1 = idSymbolBuilder->Commit();

            idSymbolBuilder->Destroy();
        }
    }
}

// #include <uf_drf.h>
// NXOpen::Point3d GetDimensionInformation(tag_t dim_tag) {
//     auto* theSession    = NXOpen::Session::GetSession();
//     auto* workPart      = theSession->Parts()->Work();
//     auto* listingWindow = theSession->ListingWindow();
//     listingWindow->Open();

//     UF_DRF_dim_info_t* dim_info      = nullptr;
//     int                dim_subtype   = 0;
//     double             dim_origin[3] = {0.0, 0.0, 0.0};
//     UF_CALL(UF_DRF_ask_dim_info(dim_tag, &dim_subtype, dim_origin, &dim_info));
//     listingWindow->WriteLine(std::to_string(dim_origin[0]));
//     listingWindow->WriteLine(std::to_string(dim_origin[1]));
//     listingWindow->WriteLine(std::to_string(dim_origin[2]));
//     return NXOpen::Point3d {dim_origin[0], dim_origin[1], dim_origin[2]};
// }


// void TestAnnotationType() {
//     auto*                 theUI            = NXOpen::UI::GetUI();
//     auto*                 selectionManager = theUI->SelectionManager();
//     NXOpen::TaggedObject* selectedObject;
//     NXOpen::Point3d       cursor;
//     NXOpen::NXString      message {"Please select a line to be hidden"};
//     NXOpen::NXString      title {"Select lines"};
//     auto                  scope           = NXOpen::Selection::SelectionScope::SelectionScopeAnyInAssembly;
//     auto                  includeFeatures = NXOpen::Selection::SelectionType::SelectionTypeAll;
//     bool                  keepHighlighted = false;

//     auto listingWindow = NXOpen::Session::GetSession()->ListingWindow();
//     listingWindow->Open();
//     while (auto response = selectionManager->SelectTaggedObject(message, title, scope, includeFeatures, keepHighlighted, &selectedObject, &cursor)) {
//         if (response == NXOpen::Selection::Response::ResponseCancel || response == NXOpen::Selection::Response::ResponseBack)
//             break;

//         if (selectedObject == nullptr)
//             break;

//         tag_t tag = selectedObject->Tag();

//         // sli:OPEN API to get the origin of annotation
//         //auto point1 = GetDimensionInformation(tag);
//         //CreateBalloonPmiNote(point1);
//         std::stringstream ss;


//         //auto annotationType = GetConcreteAnnotationType(tag);
//         auto annotationDimension = GetDimensionFromTag(tag);
//         std::vector<NXOpen::NXString> mainTextLines, dualTextLines;
//         annotationDimension->GetDimensionText(mainTextLines, dualTextLines);
//         for (auto& text : mainTextLines) {
//             //listingWindow->WriteLine("555");
//             listingWindow->WriteLine(text);
//         }
//         //auto appendedText        = annotationDimension->GetAppendedText();
//         //auto beforeText          = appendedText->GetBeforeText();
//         //auto aboveText           = appendedText->GetAboveText();
//         //auto afterText           = appendedText->GetAfterText();
//         //auto belowText           = appendedText->GetBelowText();
//         //listingWindow->WriteLine(std::to_string(static_cast<int>(annotationType)));
        

//         // Layer
//         auto layer = annotationDimension->Layer();
//         listingWindow->WriteLine("Layer: ");
//         listingWindow->WriteLine(std::to_string(layer).c_str());

//         // Font
//         auto lineFont = annotationDimension->LineFont();
//         listingWindow->WriteLine("Font: ");
//         listingWindow->WriteLine(std::to_string(static_cast<int>(lineFont)).c_str());

//         // Object Origin
//         auto annotationOrigin = annotationDimension->AnnotationOrigin();
//         auto x                = annotationOrigin.X;
//         auto y                = annotationOrigin.Y;
//         auto z                = annotationOrigin.Z;
//         listingWindow->WriteLine("Object Origin: ");
//         listingWindow->WriteLine(std::to_string(x).c_str());
//         listingWindow->WriteLine(std::to_string(y).c_str());
//         listingWindow->WriteLine(std::to_string(z).c_str());

//         CreateBalloonPmiNote(annotationOrigin);
//         //createPmiIdObject(annotationDimension, annotationOrigin);
// #if 0

//         // Text Origin
//         NXOpen::Point3d origin;
//         annotationDimension->GetAssociativeOrigin(&origin);
//         x = origin.X;
//         y = origin.Y;
//         z = origin.Z;
//         listingWindow->WriteLine("Origin: ");
//         listingWindow->WriteLine(std::to_string(x).c_str());
//         listingWindow->WriteLine(std::to_string(y).c_str());
//         listingWindow->WriteLine(std::to_string(z).c_str());


        
        

//         // Text Angle
//         auto workPart = NXOpen::Session::GetSession()->Parts()->Work();
//         auto dimensionBuilder = workPart->Dimensions()->CreateLinearDimensionBuilder(annotationDimension);
//         auto dimensionStyle   = dimensionBuilder->Style()->DimensionStyle();
//         auto textAngle = dimensionStyle->TextAngle();
//         listingWindow->WriteLine("Text Angle: ");
//         listingWindow->WriteLine(std::to_string(textAngle).c_str());

            

//         //std::vector<NXOpen::NXObject*> selectedObjects {annotationDimension};
//         //NXOpen::Session::GetSession()->Information()->DisplayObjectsDetails(selectedObjects);
        
        
//         //for (auto& attribute : attributeInformationVector) {
//         //    auto type = attribute.Type;
//         //    int  x    = 100;
//         //}
// #endif
//     }
// }

// std::vector<NXOpen::NXString> GetViewNames(NXOpen::Part* part) {
//     std::vector<NXOpen::NXString> viewNames;
//     for (const auto view: *(part->Views())) {
//         viewNames.emplace_back(view->Name());
//     }
//     return viewNames;
// }

// void PartFileInformation() {
//     auto* theSession     = NXOpen::Session::GetSession();
//     auto* workPart       = theSession->Parts()->Work();
//     auto  partFullPath   = workPart->FullPath();
//     auto  partIdRevision = workPart->Leaf();

//     auto* listingWindow = theSession->ListingWindow();
//     listingWindow->Open();
//     listingWindow->WriteLine(partFullPath);
//     listingWindow->WriteLine(partIdRevision);

//     auto viewNames = GetViewNames(workPart);
//     for (const auto v : viewNames)
//         listingWindow->WriteLine(v);

//     // get all views
//     // ATT: view MUST be displayable in order to ask visible objects
//     auto viewCollection = workPart->ModelingViews();
//     for (const auto view : *viewCollection) {
//         auto viewName = view->Name();
//         listingWindow->WriteLine(viewName);
//         auto displayableObject = view->AskVisibleObjects();
//         listingWindow->WriteLine(std::to_string(displayableObject.size()));
//     }
// }

// void GetDisplayInstances() {
//     auto* theSession    = NXOpen::Session::GetSession();
//     auto* workPart      = theSession->Parts()->Work();
//     auto* listingWindow = theSession->ListingWindow();
//     listingWindow->Open();

//     auto* pmiCollection = workPart->PmiManager()->Pmis();
//     for (const auto& pmi : *pmiCollection) {
//         auto displayAnnotations = pmi->GetDisplayInstances();
//         //auto displayInstances = pmi->GetDisplayInstances();
//         //int  count            = 0;
//         for (auto const& annotation : displayAnnotations) {
//             auto views = annotation->GetViews();
//             for (const auto& view : views) {
//                 listingWindow->WriteLine(view->Name());
//             }
//             listingWindow->WriteLine("\n");
//         }
//         //listingWindow->WriteLine(std::to_string(name.size()));
//     }
// }

// void GetDimensions() {
//     auto* theSession    = NXOpen::Session::GetSession();
//     auto* workPart      = theSession->Parts()->Work();
//     auto* listingWindow = theSession->ListingWindow();
//     listingWindow->Open();

//     int  pmiDimensionCount               = 0;
//     auto dimensionCollection = workPart->Dimensions();
//     for (auto dimension : *dimensionCollection) {
//         //std::string name = dimension->AttributeTypeString();
//         auto dimensionType = GetConcreteDimensionType(dimension);
//         pmiDimensionCount++;
//     }
//     listingWindow->WriteLine(std::to_string(pmiDimensionCount));

// }


// extern "C" DllExport void ufusr(char* param, int* retCode, int paramLen) {
//     try {
//         // MUST be called to prepare the Open C API environment
//         // UF_is_initialized() check if the environment already properly initialized
//         UF_initialize();

//         //Example2Curves(workPart, listingWindow);
//         //Example4ReadProperties(workPart, listingWindow);

//         //CreateBalloonManually();

//         //PartFileInformation();

//         //GetDimensions();

//         TestAnnotationType();

//         //GetDisplayInstances();


//         UF_terminate();
//     }
//     catch (const NXOpen::NXException& ne) {
//         std::string err {ne.Message()};
//         int         x = 100;
//     }
// }


#include <NXOpen/ListingWindow.hxx>
#include <NXOpen/NXException.hxx>
#include <NXOpen/Session.hxx>
#include "qt_singleton_window.h"

// Demonstration function for the new balloon architecture
void DemonstrateNewBalloonArchitecture() {
    try {
        auto* theLW = NXOpen::Session::GetSession()->ListingWindow();
        auto* qtWindow = QtSingletonWindow::getInstance();

        theLW->WriteLine("=== Demonstrating New Balloon Architecture ===");
        if (qtWindow) {
            qtWindow->addMessage("=== Demonstrating New Balloon Architecture ===");
        }

        // Example 1: Using Factory to create a simple balloon
        theLW->WriteLine("1. Creating balloon using Factory pattern...");
        if (qtWindow) {
            qtWindow->addMessage("1. Creating balloon using Factory pattern...");
        }

        auto simpleBalloon = BalloonFactory::createAtPosition(Point3D(100, 100, 0), "Factory Balloon");
        if (simpleBalloon && simpleBalloon->isValid()) {
            auto nxObject = simpleBalloon->create();
            theLW->WriteLine("   ✓ Factory balloon created successfully");
            if (qtWindow) {
                qtWindow->addMessage("   ✓ Factory balloon created successfully");
            }
        }

        // Example 2: Using Builder pattern to customize balloon
        theLW->WriteLine("2. Creating balloon using Builder pattern...");
        if (qtWindow) {
            qtWindow->addMessage("2. Creating balloon using Builder pattern...");
        }

        auto customBalloon = BalloonFactory::createBalloon(
            BalloonType::PMI_BALLOON,
            BalloonBuilder()
                .setId(200)
                .setText("Custom Balloon")
                .setPosition(150, 150, 0)
                .setSize(18.0)
                .setTextColor("Yellow")
                .setBorderColor("Red")
                .useHighlightSettings()
                .addAdditionalText("Built with Builder Pattern")
        );

        if (customBalloon && customBalloon->isValid()) {
            auto nxObject = customBalloon->create();
            theLW->WriteLine("   ✓ Builder balloon created successfully");
            if (qtWindow) {
                qtWindow->addMessage("   ✓ Builder balloon created successfully");
            }
        }

        // Example 3: Using different preset configurations
        theLW->WriteLine("3. Creating balloons with preset configurations...");
        if (qtWindow) {
            qtWindow->addMessage("3. Creating balloons with preset configurations...");
        }

        // PMI Balloon with default settings
        auto pmiBalloon = BalloonFactory::createBalloon(
            BalloonType::PMI_BALLOON,
            BalloonBuilder()
                .setId(300)
                .setText("PMI-300")
                .setPosition(200, 100, 0)
                .useDefaultPmiSettings()
        );

        // ID Symbol balloon
        auto idSymbol = BalloonFactory::createBalloon(
            BalloonType::ID_SYMBOL,
            BalloonBuilder()
                .setId(400)
                .setText("ID-400")
                .setPosition(250, 100, 0)
                .useIdSymbolSettings()
        );

        if (pmiBalloon && pmiBalloon->isValid()) {
            pmiBalloon->create();
            theLW->WriteLine("   ✓ PMI balloon with default settings created");
            if (qtWindow) {
                qtWindow->addMessage("   ✓ PMI balloon with default settings created");
            }
        }

        if (idSymbol && idSymbol->isValid()) {
            idSymbol->create();
            theLW->WriteLine("   ✓ ID Symbol balloon created");
            if (qtWindow) {
                qtWindow->addMessage("   ✓ ID Symbol balloon created");
            }
        }

        theLW->WriteLine("=== New Balloon Architecture Demo Complete ===");
        if (qtWindow) {
            qtWindow->addMessage("=== New Balloon Architecture Demo Complete ===");
            qtWindow->addMessage("Check the NX graphics area to see the created balloons!");
        }

    } catch (const std::exception& ex) {
        auto* theLW = NXOpen::Session::GetSession()->ListingWindow();
        std::string errorMsg = "Error in balloon demo: " + std::string(ex.what());
        theLW->WriteLine(errorMsg);

        auto* qtWindow = QtSingletonWindow::getInstance();
        if (qtWindow) {
            qtWindow->addMessage(QString::fromStdString(errorMsg));
        }
    }
}

extern "C" __declspec(dllexport) void ufusr(char* param, int* retcod, int param_len) {
    try {
        // Initialize Qt Application Manager first
        if (!QtApplicationManager::getInstance().initialize()) {
            // If Qt initialization fails, fall back to NX listing window
            NXOpen::Session::GetSession()->ListingWindow()->WriteLine("Error: Failed to initialize Qt Application");
            NXOpen::Session::GetSession()->ListingWindow()->WriteLine("This may be due to missing Qt platform plugins.");
            NXOpen::Session::GetSession()->ListingWindow()->WriteLine("Check that platforms/qwindowsd.dll exists in the same directory as tw_demo.dll");
            return;
        }

        // Get the NX session
        NXOpen::Session*       theSession = NXOpen::Session::GetSession();
        NXOpen::ListingWindow* theLW      = theSession->ListingWindow();

        // Open the Listing Window
        if (!theLW->IsOpen()) {
            theLW->Open();
        }

        // Write debug messages to NX listing window
        theLW->WriteLine("Debug: Plugin initialized.");
        theLW->WriteLine("Debug: Qt Application initialized successfully.");

        // Get the Qt singleton window and show it
        QtSingletonWindow* qtWindow = QtSingletonWindow::getInstance();
        if (qtWindow) {
            qtWindow->addMessage("NX Plugin DLL loaded successfully!");
            qtWindow->addMessage("Qt Singleton Window is now available.");
            qtWindow->addMessage("This window demonstrates the singleton pattern.");
            qtWindow->showWindow();

            theLW->WriteLine("Debug: Qt Singleton Window created and shown.");
        }

        // Example plugin logic
        theLW->WriteLine("Debug: Performing some operation...");

        // Add message to Qt window about the operation
        if (qtWindow) {
            qtWindow->addMessage("Starting CreateBalloonManually operation...");
        }

        CreateBalloonManually();

        // Demonstrate new balloon architecture
        DemonstrateNewBalloonArchitecture();

        // Notify completion
        if (qtWindow) {
            qtWindow->addMessage("CreateBalloonManually operation completed.");
            qtWindow->addMessage("You can now interact with the Qt window!");
            qtWindow->addMessage("This window will persist until the DLL is unloaded.");
        }
    }
    catch (const NXOpen::NXException& ex) {
        // Handle errors
        std::string errorMsg = "NX Error: " + std::string(ex.Message());
        NXOpen::Session::GetSession()->ListingWindow()->WriteLine(errorMsg);

        // Also log to Qt window if available
        QtSingletonWindow* qtWindow = QtSingletonWindow::getInstance();
        if (qtWindow) {
            qtWindow->addMessage(QString::fromStdString(errorMsg));
        }
    }
    catch (const std::exception& ex) {
        // Handle standard exceptions
        std::string errorMsg = "Standard Error: " + std::string(ex.what());
        NXOpen::Session::GetSession()->ListingWindow()->WriteLine(errorMsg);

        // Also log to Qt window if available
        QtSingletonWindow* qtWindow = QtSingletonWindow::getInstance();
        if (qtWindow) {
            qtWindow->addMessage(QString::fromStdString(errorMsg));
        }
    }
}

extern "C" __declspec(dllexport) int ufusr_ask_unload() {
    return (int) NXOpen::Session::LibraryUnloadOptionImmediately;
}

extern "C" __declspec(dllexport) void ufusr_cleanup(void) {
    try {
        // Notify Qt window about cleanup
        QtSingletonWindow* qtWindow = QtSingletonWindow::getInstance();
        if (qtWindow) {
            qtWindow->addMessage("DLL is being unloaded. Cleaning up Qt resources...");
        }

        // Cleanup Qt Application Manager and all Qt resources
        QtApplicationManager::getInstance().cleanup();

        // Log to NX listing window
        NXOpen::Session::GetSession()->ListingWindow()->WriteLine("Debug: Qt resources cleaned up successfully.");
    }
    catch (const std::exception& ex) {
        // Handle cleanup errors
        NXOpen::Session::GetSession()->ListingWindow()->WriteLine("Error during cleanup: " + std::string(ex.what()));
    }
}