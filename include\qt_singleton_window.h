#pragma once

#include <QMainWindow>
#include <QApplication>
#include <QLabel>
#include <QPushButton>
#include <QVBoxLayout>
#include <QWidget>
#include <QTextEdit>
#include <memory>
#include <mutex>

class QtSingletonWindow : public QMainWindow
{
    Q_OBJECT

    // Allow QtApplicationManager to access private members for cleanup
    friend class QtApplicationManager;

public:
    // Singleton access method
    static QtSingletonWindow* getInstance();
    
    // Show the window
    void showWindow();
    
    // Hide the window
    void hideWindow();
    
    // Add a message to the window
    void addMessage(const QString& message);
    
    // Check if window is visible
    bool isWindowVisible() const;

    // Destructor (public for std::unique_ptr)
    ~QtSingletonWindow() override;

private slots:
    void onCloseButtonClicked();
    void onClearButtonClicked();

private:
    // Private constructor for singleton
    QtSingletonWindow(QWidget* parent = nullptr);
    
    // Delete copy constructor and assignment operator
    QtSingletonWindow(const QtSingletonWindow&) = delete;
    QtSingletonWindow& operator=(const QtSingletonWindow&) = delete;
    
    // Setup UI
    void setupUI();
    
    // Static instance
    static std::unique_ptr<QtSingletonWindow> s_instance;
    static std::mutex s_mutex;
    
    // UI components
    QWidget* m_centralWidget;
    QVBoxLayout* m_layout;
    QLabel* m_titleLabel;
    QTextEdit* m_messageArea;
    QPushButton* m_closeButton;
    QPushButton* m_clearButton;
};

// Qt Application Manager - handles QApplication lifecycle
class QtApplicationManager
{
public:
    // Get the singleton instance
    static QtApplicationManager& getInstance();
    
    // Initialize Qt Application (call this when DLL loads)
    bool initialize();
    
    // Cleanup Qt Application (call this when DLL unloads)
    void cleanup();
    
    // Check if Qt is initialized
    bool isInitialized() const;
    
    // Get the QApplication instance
    QApplication* getApplication() const;

private:
    QtApplicationManager() = default;
    ~QtApplicationManager() = default;
    
    // Delete copy constructor and assignment operator
    QtApplicationManager(const QtApplicationManager&) = delete;
    QtApplicationManager& operator=(const QtApplicationManager&) = delete;
    
    std::unique_ptr<QApplication> m_app;
    bool m_initialized = false;
    static std::mutex s_mutex;
    
    // Dummy argc/argv for QApplication
    int m_argc = 1;
    char m_appName[16] = "nx_plugin";
    char* m_argv[2] = {m_appName, nullptr};
};
