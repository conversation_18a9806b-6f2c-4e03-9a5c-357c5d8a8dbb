#include <common_header.hpp>
#include <NXOpen/Annotations_DimensionCollection.hxx>
#include <NXOpen/Annotations_DimensionCollection.hxx>
#include <NXOpen/Annotations_LeaderDataList.hxx>
#include <NXOpen/Annotations_PmiAttributeCollection.hxx>
#include <NXOpen/SelectDisplayableObject.hxx>

void temp() {
    tag_t                 pmiTag       = NULL_TAG;
    NXOpen::TaggedObject* pmiTaggedObj = NXOpen::NXObjectManager::Get(pmiTag);

    if (dynamic_cast<NXOpen::Annotations::PmiArcLengthDimension*>(pmiTaggedObj) ||
        dynamic_cast<NXOpen::Annotations::PmiBaselineDimension*>(pmiTaggedObj) ||
        dynamic_cast<NXOpen::Annotations::PmiChainDimension*>(pmiTaggedObj) ||
        dynamic_cast<NXOpen::Annotations::PmiChamferDimension*>(pmiTaggedObj) ||
        dynamic_cast<NXOpen::Annotations::PmiConcentricCircleDimension*>(pmiTaggedObj) ||
        dynamic_cast<NXOpen::Annotations::PmiCylindricalDimension*>(pmiTaggedObj) ||
        dynamic_cast<NXOpen::Annotations::PmiDiameterDimension*>(pmiTaggedObj) ||
        dynamic_cast<NXOpen::Annotations::PmiFoldedRadiusDimension*>(pmiTaggedObj) ||
        dynamic_cast<NXOpen::Annotations::PmiHoleDimension*>(pmiTaggedObj) ||
        dynamic_cast<NXOpen::Annotations::PmiHorizontalDimension*>(pmiTaggedObj) ||
        dynamic_cast<NXOpen::Annotations::PmiHorizontalOrdinateDimension*>(pmiTaggedObj) ||
        dynamic_cast<NXOpen::Annotations::PmiMajorAngularDimension*>(pmiTaggedObj) ||
        dynamic_cast<NXOpen::Annotations::PmiMinorAngularDimension*>(pmiTaggedObj) ||
        dynamic_cast<NXOpen::Annotations::PmiOrdinateOriginDimension*>(pmiTaggedObj) ||
        dynamic_cast<NXOpen::Annotations::PmiParallelDimension*>(pmiTaggedObj) ||
        dynamic_cast<NXOpen::Annotations::PmiPerpendicularDimension*>(pmiTaggedObj) ||
        dynamic_cast<NXOpen::Annotations::PmiRadiusDimension*>(pmiTaggedObj) ||
        dynamic_cast<NXOpen::Annotations::PmiVerticalDimension*>(pmiTaggedObj) ||
        dynamic_cast<NXOpen::Annotations::PmiVerticalOrdinateDimension*>(pmiTaggedObj) ||
        dynamic_cast<NXOpen::Annotations::Fcf*>(pmiTaggedObj) ||
        dynamic_cast<NXOpen::Annotations::Datum*>(pmiTaggedObj) ||
        dynamic_cast<NXOpen::Annotations::PmiCustomSymbol*>(pmiTaggedObj) ||
        dynamic_cast<NXOpen::Annotations::PmiNote*>(pmiTaggedObj) ||
        dynamic_cast<NXOpen::Annotations::PmiAttribute*>(pmiTaggedObj) ||
        dynamic_cast<NXOpen::Annotations::PointTarget*>(pmiTaggedObj) ||
        dynamic_cast<NXOpen::Annotations::PmiLineWeld*>(pmiTaggedObj)) {
        auto* annotation = dynamic_cast<NXOpen::Annotations::Annotation*>(pmiTaggedObj);
    }
}


ANNOTATION_TYPE GetConcreteAnnotationType(NXOpen::Annotations::Annotation* annotation) {
    if (annotation == nullptr)
        return ANNOTATION_TYPE::NOT_ANNOTATION_TYPE;

    // 15 base dimensions
    if (dynamic_cast<NXOpen::Annotations::BaseAngularDimension*>(annotation))
        return ANNOTATION_TYPE::BASE_ANGULAR_DIMENSION;
    if (dynamic_cast<NXOpen::Annotations::BaseArcLengthDimension*>(annotation))
        return ANNOTATION_TYPE::BASE_ARC_LENGTH_DIMENSION;
    if (dynamic_cast<NXOpen::Annotations::BaseChamferDimension*>(annotation))
        return ANNOTATION_TYPE::BASE_CHAMFER_DIMENSION;
    if (dynamic_cast<NXOpen::Annotations::BaseConcentricCircleDimension*>(annotation))
        return ANNOTATION_TYPE::BASE_CONCENTRIC_CIRCLE_DIMENSION;
    if (dynamic_cast<NXOpen::Annotations::BaseCylindricalDimension*>(annotation))
        return ANNOTATION_TYPE::BASE_CYLINDRICAL_DIMENSION;
    if (dynamic_cast<NXOpen::Annotations::BaseDiameterDimension*>(annotation))
        return ANNOTATION_TYPE::BASE_DIAMETER_DIMENSION;
    if (dynamic_cast<NXOpen::Annotations::BaseFoldedRadiusDimension*>(annotation))
        return ANNOTATION_TYPE::BASE_FOLDED_RADIUS_DIMENSION;
    if (dynamic_cast<NXOpen::Annotations::BaseHoleDimension*>(annotation))
        return ANNOTATION_TYPE::BASE_HOLE_DIMENSION;
    if (dynamic_cast<NXOpen::Annotations::BaseHorizontalDimension*>(annotation))
        return ANNOTATION_TYPE::BASE_HORIZONTAL_DIMENSION;
    if (dynamic_cast<NXOpen::Annotations::BaseParallelDimension*>(annotation))
        return ANNOTATION_TYPE::BASE_PARALLEL_DIMENSION;
    if (dynamic_cast<NXOpen::Annotations::BasePerpendicularDimension*>(annotation))
        return ANNOTATION_TYPE::BASE_PERPENDICULAR_DIMENSION;
    if (dynamic_cast<NXOpen::Annotations::BaseRadiusDimension*>(annotation))
        return ANNOTATION_TYPE::BASE_RADIUS_DIMENSION;
    if (dynamic_cast<NXOpen::Annotations::BaseVerticalDimension*>(annotation))
        return ANNOTATION_TYPE::BASE_VERTICAL_DIMENSION;
    if (dynamic_cast<NXOpen::Annotations::OrdinateDimension*>(annotation))
        return ANNOTATION_TYPE::ORDINATE_DIMENSION;
    if (dynamic_cast<NXOpen::Annotations::OrdinateOriginDimension*>(annotation))
        return ANNOTATION_TYPE::ORDINATE_ORIGIN_DIMENSION;


    if (dynamic_cast<NXOpen::Annotations::Fcf*>(annotation))
        return ANNOTATION_TYPE::FCF;
    if (dynamic_cast<NXOpen::Annotations::Datum*>(annotation))
        return ANNOTATION_TYPE::DATUM;
    if (dynamic_cast<NXOpen::Annotations::PmiCustomSymbol*>(annotation))
        return ANNOTATION_TYPE::PMI_CUSTOM_SYMBOL;
    if (dynamic_cast<NXOpen::Annotations::PmiNote*>(annotation))
        return ANNOTATION_TYPE::PMI_NOTE;
    if (dynamic_cast<NXOpen::Annotations::PmiAttribute*>(annotation))
        return ANNOTATION_TYPE::PMI_ATTRIBUTE;
    if (dynamic_cast<NXOpen::Annotations::PointTarget*>(annotation))
        return ANNOTATION_TYPE::POINT_TARGET;
    if (dynamic_cast<NXOpen::Annotations::PmiLineWeld*>(annotation))
        return ANNOTATION_TYPE::PMI_LINE_WELD;
    return ANNOTATION_TYPE::UNKNOWN;
}

ANNOTATION_TYPE GetConcreteAnnotationType(tag_t tag) {
    NXOpen::TaggedObject* taggedObject = NXOpen::NXObjectManager::Get(tag);
    auto* annotation   = dynamic_cast<NXOpen::Annotations::Annotation*>(taggedObject);
    return GetConcreteAnnotationType(annotation);
}

// ATT: only consider PMI dimension right now
ANNOTATION_TYPE GetConcreteDimensionType(NXOpen::Annotations::Dimension* dimension) {
    auto* annotation   = dynamic_cast<NXOpen::Annotations::Annotation*>(dimension);
    return GetConcreteAnnotationType(annotation);
}

NXOpen::Annotations::Dimension* GetDimensionFromTag(tag_t tag) {
    NXOpen::TaggedObject* taggedObject = NXOpen::NXObjectManager::Get(tag);
    return dynamic_cast<NXOpen::Annotations::Dimension*>(taggedObject);
}


// Create PMI Balloon of BCT
#include <NXOpen/Annotations_BalloonNote.hxx>
#include <NXOpen/Annotations_BalloonNoteBuilder.hxx>
void createPmiIdObject(NXOpen::Annotations::Dimension* dimension, NXOpen::Point3d origin) {
    auto theSession = NXOpen::Session::GetSession();
    auto workPart   = theSession->Parts()->Work();
    auto pmiManager = workPart->PmiManager();

    NXOpen::Annotations::BalloonNote* balloonNoteNull = nullptr;
    auto balloonNoteBuilder = pmiManager->PmiAttributes()->CreateBalloonNoteBuilder(balloonNoteNull);
    balloonNoteBuilder->Origin()->SetInferRelativeToGeometry(true);
    balloonNoteBuilder->Origin()->SetAnchor(NXOpen::Annotations::OriginBuilder::AlignmentPositionMidCenter);
    balloonNoteBuilder->Origin()->Plane()->SetPlaneMethod(NXOpen::Annotations::PlaneBuilder::PlaneMethodTypeModelView);
    balloonNoteBuilder->SetTitle("PMI Linear Dimension(222)");
    balloonNoteBuilder->SetCategory("User Defined");   // ("Category: BCT");
    balloonNoteBuilder->SetIdentifier("User Defined"); // ("Identifier: BCT");
    balloonNoteBuilder->SetRevision("-");              // ("Revision: BCT");
    balloonNoteBuilder->SetBalloonText("222");
    std::vector<NXOpen::NXString> text(1);
    text[0] = "Text: BCT";
    balloonNoteBuilder->SetText(text);

    NXOpen::Annotations::StyleBuilder*          styleBuilder          = balloonNoteBuilder->Style();
    NXOpen::Annotations::LetteringStyleBuilder* letteringStyleBuilder = styleBuilder->LetteringStyle();
    NXOpen::Annotations::Lettering              general_text_pmi;

    // sli: implement  chx_pmi.textPreferences(&general_text_pmi))
    //auto* preferences   = dimension->GetLetteringPreferences();
    //auto  dimensionText = preferences->GetDimensionText(); // for dimension
    ////auto  generalText   = preferences->GetGeneralText();  // for other PMI
    //NXOpen::Annotations::Lettering generalLettering = dimensionText;

    auto leaderData = workPart->Annotations()->CreateLeaderData();
    leaderData->SetStubSize(5.0);
    leaderData->SetArrowhead(NXOpen::Annotations::LeaderData::ArrowheadTypeFilledArrow);
    leaderData->SetStubSide(NXOpen::Annotations::LeaderSideInferred);
    balloonNoteBuilder->Leader()->Leaders()->Append(leaderData);

    // TODO: need to check the return view vector
    //UGPmiRootClass::preferedOriginData()
    auto modelingView = dimension->GetViews()[0];
    NXOpen::Annotations::Annotation::AssociativeOriginData associativeOriginData;
    associativeOriginData.View = modelingView;
    associativeOriginData.ViewOfGeometry = modelingView;
    associativeOriginData.AssociatedView = modelingView;

    associativeOriginData.OriginType     = NXOpen::Annotations::AssociativeOriginTypeAttachedToStack;
    associativeOriginData.PointOnGeometry = nullptr;
    associativeOriginData.VertAnnotation          = nullptr;
    associativeOriginData.VertAlignmentPosition   = NXOpen::Annotations::AlignmentPositionTopLeft;
    associativeOriginData.HorizAnnotation         = nullptr;
    associativeOriginData.HorizAlignmentPosition  = NXOpen::Annotations::AlignmentPositionTopLeft;
    associativeOriginData.AlignedAnnotation       = nullptr;
    associativeOriginData.DimensionLine           = 0;
    associativeOriginData.AssociatedPoint         = nullptr;
    associativeOriginData.OffsetAnnotation        = nullptr;
    associativeOriginData.OffsetAlignmentPosition = NXOpen::Annotations::AlignmentPositionTopLeft;
    associativeOriginData.XOffsetFactor           = 0.0;
    associativeOriginData.YOffsetFactor           = 0.0;
    associativeOriginData.StackAlignmentPosition  = NXOpen::Annotations::StackAlignmentPositionRight;

    if (!dimension->IsOriginCentered()) {
        if (dimension->LeaderOrientation() == NXOpen::Annotations::LeaderOrientationFromRight) {
            associativeOriginData.StackAlignmentPosition = NXOpen::Annotations::StackAlignmentPositionLeft;
        }
    }
    // sli: this is for non-dimension
    //else if (anno->LeaderOrientation() == NXOpen::Annotations::LeaderOrientationFromRight) {
    //    assoc_origin->StackAlignmentPosition = NXOpen::Annotations::StackAlignmentPositionLeft;
    balloonNoteBuilder->Origin()->SetAssociativeOrigin(associativeOriginData);

    // ---------------------------------------------------------
    // Doing the leader stuff ...
    // ---------------------------------------------------------
    leaderData->Leader()->SetValue(dimension, modelingView, origin);

    balloonNoteBuilder->Origin()->Origin()->SetValue(dimension, modelingView, origin);
    balloonNoteBuilder->Commit();
    balloonNoteBuilder->Destroy();
}