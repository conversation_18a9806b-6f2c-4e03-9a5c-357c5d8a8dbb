@echo off
REM Test script to verify Qt plugin loading
REM This script helps diagnose Qt platform plugin issues

echo Testing Qt Plugin Loading for tw_demo.dll
echo ==========================================

REM Set the directory where tw_demo.dll is located
set DLL_DIR=build\Debug

echo.
echo 1. Checking if tw_demo.dll exists...
if exist "%DLL_DIR%\tw_demo.dll" (
    echo ✓ tw_demo.dll found
) else (
    echo ✗ tw_demo.dll NOT found in %DLL_DIR%
    goto :error
)

echo.
echo 2. Checking Qt Core libraries...
if exist "%DLL_DIR%\Qt6Cored.dll" (
    echo ✓ Qt6Cored.dll found
) else (
    echo ✗ Qt6Cored.dll NOT found
    goto :error
)

if exist "%DLL_DIR%\Qt6Guid.dll" (
    echo ✓ Qt6Guid.dll found
) else (
    echo ✗ Qt6Guid.dll NOT found
    goto :error
)

if exist "%DLL_DIR%\Qt6Widgetsd.dll" (
    echo ✓ Qt6Widgetsd.dll found
) else (
    echo ✗ Qt6Widgetsd.dll NOT found
    goto :error
)

echo.
echo 3. Checking platform plugins...
if exist "%DLL_DIR%\platforms\qwindowsd.dll" (
    echo ✓ qwindowsd.dll found in platforms directory
) else (
    echo ✗ qwindowsd.dll NOT found in platforms directory
    goto :error
)

echo.
echo 4. Checking other plugin directories...
if exist "%DLL_DIR%\styles" (
    echo ✓ styles directory exists
) else (
    echo ✗ styles directory missing
)

if exist "%DLL_DIR%\imageformats" (
    echo ✓ imageformats directory exists
) else (
    echo ✗ imageformats directory missing
)

echo.
echo 5. Setting Qt environment variables for testing...
set QT_PLUGIN_PATH=%CD%\%DLL_DIR%
set QT_DEBUG_PLUGINS=1
set QT_LOGGING_RULES=qt.qpa.plugin.debug=true

echo QT_PLUGIN_PATH=%QT_PLUGIN_PATH%
echo QT_DEBUG_PLUGINS=%QT_DEBUG_PLUGINS%

echo.
echo 6. Listing all files in DLL directory...
dir "%DLL_DIR%\*.dll" /b

echo.
echo 7. Listing platform plugins...
dir "%DLL_DIR%\platforms\*.dll" /b

echo.
echo ==========================================
echo All checks passed! Qt plugins should load correctly.
echo.
echo If you still get platform plugin errors:
echo 1. Make sure you're loading the DLL from the correct directory
echo 2. Check that all Qt DLLs are the same version (Debug vs Release)
echo 3. Verify that the Visual C++ Redistributable is installed
echo 4. Try setting QT_PLUGIN_PATH environment variable manually
echo.
echo To test manually, set these environment variables:
echo   set QT_PLUGIN_PATH=%CD%\%DLL_DIR%
echo   set QT_DEBUG_PLUGINS=1
echo.
goto :end

:error
echo.
echo ==========================================
echo ERROR: Missing required files!
echo.
echo Please run one of the following to deploy Qt libraries:
echo   1. cmake --build build --config Debug
echo   2. deploy_qt.bat
echo   3. Manual deployment with windeployqt
echo.

:end
pause
