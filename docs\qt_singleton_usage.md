# Qt Singleton Window Usage Guide

## Overview

This project implements a Qt singleton window that opens when the NX plugin DLL is loaded. The window follows the singleton pattern, ensuring only one instance exists throughout the plugin's lifetime.

## Features

- **Singleton Pattern**: Only one window instance can exist
- **Thread-Safe**: Uses mutex for thread-safe singleton access
- **Automatic Initialization**: Qt Application is automatically initialized when needed
- **Message Logging**: Provides a text area for logging messages with timestamps
- **Persistent**: Window persists until the DLL is unloaded
- **User Interaction**: Users can hide/show the window and clear messages

## Architecture

### Components

1. **QtApplicationManager**: Manages the QApplication lifecycle
2. **QtSingletonWindow**: The main singleton window class

### Key Classes

#### QtApplicationManager
- Singleton class that manages QApplication initialization and cleanup
- Thread-safe initialization
- Automatic cleanup when DLL unloads

#### QtSingletonWindow
- Main window with message logging capabilities
- Singleton pattern implementation
- Qt widgets for user interaction

## Usage Examples

### Basic Usage

```cpp
#include "qt_singleton_window.h"

// Get the singleton instance (creates it if it doesn't exist)
QtSingletonWindow* window = QtSingletonWindow::getInstance();

// Add messages to the window
window->addMessage("Plugin operation started");
window->addMessage("Processing data...");
window->addMessage("Operation completed successfully");

// Show the window
window->showWindow();

// Hide the window
window->hideWindow();

// Check if window is visible
if (window->isWindowVisible()) {
    window->addMessage("Window is currently visible");
}
```

### Integration in NX Plugin

The window is automatically created and shown when the DLL loads:

```cpp
extern "C" __declspec(dllexport) void ufusr(char* param, int* retcod, int param_len) {
    try {
        // Initialize Qt Application Manager
        if (!QtApplicationManager::getInstance().initialize()) {
            // Handle initialization failure
            return;
        }

        // Get singleton window and use it
        QtSingletonWindow* qtWindow = QtSingletonWindow::getInstance();
        if (qtWindow) {
            qtWindow->addMessage("NX Plugin loaded successfully!");
            qtWindow->showWindow();
        }

        // Your plugin logic here...
        
    } catch (const std::exception& ex) {
        // Error handling
        QtSingletonWindow* qtWindow = QtSingletonWindow::getInstance();
        if (qtWindow) {
            qtWindow->addMessage(QString("Error: %1").arg(ex.what()));
        }
    }
}
```

### Cleanup

Cleanup is automatically handled when the DLL unloads:

```cpp
extern "C" __declspec(dllexport) void ufusr_cleanup(void) {
    // Automatic cleanup of Qt resources
    QtApplicationManager::getInstance().cleanup();
}
```

## Window Features

### Message Area
- Displays timestamped messages
- Auto-scrolls to show latest messages
- Read-only text area with monospace font

### Buttons
- **Clear Messages**: Clears all messages from the display
- **Hide Window**: Hides the window (doesn't destroy it)

### Window Properties
- Title: "NX Plugin - Qt Singleton Window"
- Minimum size: 600x400 pixels
- Default size: 800x600 pixels
- Resizable and movable

## Thread Safety

The implementation is thread-safe:
- Singleton access is protected by mutex
- Qt Application initialization is thread-safe
- Window operations should be called from the main thread

## Best Practices

1. **Always check for null pointers** when getting the singleton instance
2. **Use try-catch blocks** around Qt operations
3. **Add meaningful messages** with context information
4. **Don't manually delete** the singleton instance
5. **Let the cleanup happen automatically** when the DLL unloads

## Troubleshooting

### Common Issues

1. **Window doesn't appear**: Check if Qt initialization succeeded
2. **Messages not showing**: Ensure you're calling `addMessage()` correctly
3. **Crashes on cleanup**: Make sure you're not accessing Qt objects after cleanup

### Debug Information

The plugin logs debug information to both:
- NX Listing Window
- Qt Singleton Window

Check both locations for troubleshooting information.
