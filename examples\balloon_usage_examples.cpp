// Balloon Architecture Usage Examples
// This file demonstrates how to use the new balloon architecture

#include "balloon.hpp"
#include <iostream>
#include <vector>
#include <memory>

// Example 1: Basic balloon creation using factory
void example1_basic_factory() {
    std::cout << "=== Example 1: Basic Factory Usage ===" << std::endl;
    
    try {
        // Create a simple PMI balloon
        auto balloon = BalloonFactory::createPmiBalloon();
        
        // Set basic properties
        balloon->setText("100");
        balloon->setPosition(Point3D(100, 100, 0));
        
        if (balloon->isValid()) {
            auto nxObject = balloon->create();
            std::cout << "✓ Basic PMI balloon created successfully" << std::endl;
        }
        
        // Create an ID symbol
        auto idSymbol = BalloonFactory::createIdSymbol();
        idSymbol->setText("ID-200");
        idSymbol->setPosition(Point3D(200, 100, 0));
        
        if (idSymbol->isValid()) {
            idSymbol->create();
            std::cout << "✓ ID Symbol created successfully" << std::endl;
        }
        
    } catch (const std::exception& e) {
        std::cout << "✗ Error in example 1: " << e.what() << std::endl;
    }
}

// Example 2: Using builder pattern for customization
void example2_builder_pattern() {
    std::cout << "\n=== Example 2: Builder Pattern Usage ===" << std::endl;
    
    try {
        // Create a highly customized balloon using builder
        auto customBalloon = BalloonFactory::createBalloon(
            BalloonType::PMI_BALLOON,
            BalloonBuilder()
                .setId(300)
                .setText("Custom Balloon")
                .setTitle("Custom PMI Balloon")
                .setPosition(150, 150, 0)
                .setSize(20.0)
                .setTextAngle(45.0)
                .setTextColor("Yellow")
                .setBorderColor("Red")
                .setBackgroundColor("Black")
                .setHasLeader(true)
                .setArrowType(ArrowType::FILLED_ARROW)
                .setStubSize(8.0)
                .addAdditionalText("Line 1: Custom text")
                .addAdditionalText("Line 2: More info")
        );
        
        if (customBalloon && customBalloon->isValid()) {
            customBalloon->create();
            std::cout << "✓ Custom balloon created with builder pattern" << std::endl;
        }
        
    } catch (const std::exception& e) {
        std::cout << "✗ Error in example 2: " << e.what() << std::endl;
    }
}

// Example 3: Using preset configurations
void example3_preset_configurations() {
    std::cout << "\n=== Example 3: Preset Configurations ===" << std::endl;
    
    try {
        // PMI balloon with default settings
        auto pmiBalloon = BalloonFactory::createBalloon(
            BalloonType::PMI_BALLOON,
            BalloonBuilder()
                .setId(400)
                .setText("PMI-400")
                .setPosition(50, 200, 0)
                .useDefaultPmiSettings()
        );
        
        // ID Symbol with preset settings
        auto idSymbol = BalloonFactory::createBalloon(
            BalloonType::ID_SYMBOL,
            BalloonBuilder()
                .setId(500)
                .setText("ID-500")
                .setPosition(150, 200, 0)
                .useIdSymbolSettings()
        );
        
        // Highlighted balloon for important annotations
        auto highlightBalloon = BalloonFactory::createBalloon(
            BalloonType::PMI_BALLOON,
            BalloonBuilder()
                .setId(600)
                .setText("CRITICAL")
                .setPosition(250, 200, 0)
                .useHighlightSettings()
        );
        
        // Create all balloons
        if (pmiBalloon && pmiBalloon->isValid()) {
            pmiBalloon->create();
            std::cout << "✓ PMI balloon with default settings created" << std::endl;
        }
        
        if (idSymbol && idSymbol->isValid()) {
            idSymbol->create();
            std::cout << "✓ ID Symbol with preset settings created" << std::endl;
        }
        
        if (highlightBalloon && highlightBalloon->isValid()) {
            highlightBalloon->create();
            std::cout << "✓ Highlighted balloon created" << std::endl;
        }
        
    } catch (const std::exception& e) {
        std::cout << "✗ Error in example 3: " << e.what() << std::endl;
    }
}

// Example 4: Convenience factory methods
void example4_convenience_methods() {
    std::cout << "\n=== Example 4: Convenience Factory Methods ===" << std::endl;
    
    try {
        // Create balloon at specific position with text
        auto positionBalloon = BalloonFactory::createAtPosition(
            Point3D(300, 300, 0), 
            "Position Balloon"
        );
        
        if (positionBalloon && positionBalloon->isValid()) {
            positionBalloon->create();
            std::cout << "✓ Position-based balloon created" << std::endl;
        }
        
        // Note: For dimension and FCF examples, you would need actual NX objects
        // auto dimensionBalloon = BalloonFactory::createForDimension(dimension, 700);
        // auto fcfBalloon = BalloonFactory::createForFcf(fcf, 800);
        
    } catch (const std::exception& e) {
        std::cout << "✗ Error in example 4: " << e.what() << std::endl;
    }
}

// Example 5: Property management and updates
void example5_property_management() {
    std::cout << "\n=== Example 5: Property Management ===" << std::endl;
    
    try {
        // Create a balloon
        auto balloon = BalloonFactory::createPmiBalloon();
        
        // Set initial properties
        balloon->setText("Initial Text");
        balloon->setPosition(Point3D(400, 100, 0));
        
        // Create the balloon
        if (balloon->isValid()) {
            balloon->create();
            std::cout << "✓ Initial balloon created" << std::endl;
            
            // Update properties
            balloon->setText("Updated Text");
            balloon->setPosition(Point3D(450, 100, 0));
            
            // Update the balloon (recreates with new properties)
            if (balloon->update()) {
                std::cout << "✓ Balloon properties updated successfully" << std::endl;
            }
            
            // Get current properties
            auto props = balloon->getProperties();
            std::cout << "Current balloon text: " << props.text << std::endl;
            std::cout << "Current position: (" << props.position.x << ", " 
                      << props.position.y << ", " << props.position.z << ")" << std::endl;
        }
        
    } catch (const std::exception& e) {
        std::cout << "✗ Error in example 5: " << e.what() << std::endl;
    }
}

// Example 6: Batch balloon creation
void example6_batch_creation() {
    std::cout << "\n=== Example 6: Batch Balloon Creation ===" << std::endl;
    
    try {
        std::vector<std::unique_ptr<IBalloon>> balloons;
        
        // Create multiple balloons with different configurations
        for (int i = 0; i < 5; ++i) {
            auto balloon = BalloonFactory::createBalloon(
                BalloonType::PMI_BALLOON,
                BalloonBuilder()
                    .setId(1000 + i)
                    .setText("Batch-" + std::to_string(i))
                    .setPosition(50 + i * 60, 300, 0)
                    .setSize(12.0 + i * 2)
                    .useDefaultPmiSettings()
            );
            
            if (balloon && balloon->isValid()) {
                balloon->create();
                balloons.push_back(std::move(balloon));
            }
        }
        
        std::cout << "✓ Created " << balloons.size() << " balloons in batch" << std::endl;
        
    } catch (const std::exception& e) {
        std::cout << "✗ Error in example 6: " << e.what() << std::endl;
    }
}

// Example 7: Error handling and validation
void example7_error_handling() {
    std::cout << "\n=== Example 7: Error Handling and Validation ===" << std::endl;
    
    try {
        // Create balloon with invalid properties
        auto invalidBalloon = BalloonFactory::createBalloon(
            BalloonType::PMI_BALLOON,
            BalloonBuilder()
                .setId(-1)  // Invalid ID
                .setText("")  // Empty text
                .setSize(-5.0)  // Invalid size
        );
        
        if (!invalidBalloon->isValid()) {
            std::cout << "✓ Invalid balloon correctly identified" << std::endl;
        } else {
            std::cout << "✗ Validation failed to catch invalid properties" << std::endl;
        }
        
        // Create valid balloon
        auto validBalloon = BalloonFactory::createBalloon(
            BalloonType::PMI_BALLOON,
            BalloonBuilder()
                .setId(2000)
                .setText("Valid Balloon")
                .setPosition(500, 100, 0)
                .setSize(15.0)
                .useDefaultPmiSettings()
        );
        
        if (validBalloon->isValid()) {
            validBalloon->create();
            std::cout << "✓ Valid balloon created successfully" << std::endl;
        }
        
    } catch (const std::exception& e) {
        std::cout << "✓ Exception properly caught: " << e.what() << std::endl;
    }
}

// Main demonstration function
void demonstrateAllExamples() {
    std::cout << "Balloon Architecture Usage Examples\n";
    std::cout << "===================================\n" << std::endl;
    
    example1_basic_factory();
    example2_builder_pattern();
    example3_preset_configurations();
    example4_convenience_methods();
    example5_property_management();
    example6_batch_creation();
    example7_error_handling();
    
    std::cout << "\n=== All Examples Complete ===" << std::endl;
}
