#pragma once
#include <NXOpen/Annotations_Annotation.hxx>
#include <NXOpen/Annotations_AnnotationManager.hxx>
#include <NXOpen/Annotations_Datum.hxx>
#include <NXOpen/Annotations_Dimension.hxx>
#include <NXOpen/Annotations_DimensionCollection.hxx>
#include <NXOpen/Annotations_Fcf.hxx>
#include <NXOpen/Annotations_Pmi.hxx>
#include <NXOpen/Annotations_PmiArcLengthDimension.hxx>
#include <NXOpen/Annotations_PmiAttribute.hxx>
#include <NXOpen/Annotations_PmiBaselineDimension.hxx>
#include <NXOpen/Annotations_PmiChainDimension.hxx>
#include <NXOpen/Annotations_PmiChamferDimension.hxx>
#include <NXOpen/Annotations_PmiChamferDimensionBuilder.hxx>
#include <NXOpen/Annotations_PmiCollection.hxx>
#include <NXOpen/Annotations_PmiConcentricCircleDimension.hxx>
#include <NXOpen/Annotations_PmiCustomSymbol.hxx>
#include <NXOpen/Annotations_PmiCylindricalDimension.hxx>
#include <NXOpen/Annotations_PmiDiameterDimension.hxx>
#include <NXOpen/Annotations_PmiFoldedRadiusDimension.hxx>
#include <NXOpen/Annotations_PmiHoleDimension.hxx>
#include <NXOpen/Annotations_PmiHorizontalDimension.hxx>
#include <NXOpen/Annotations_PmiHorizontalOrdinateDimension.hxx>
#include <NXOpen/Annotations_PmiLineWeld.hxx>
#include <NXOpen/Annotations_PmiMajorAngularDimension.hxx>
#include <NXOpen/Annotations_PmiManager.hxx>
#include <NXOpen/Annotations_PmiMinorAngularDimension.hxx>
#include <NXOpen/Annotations_PmiNote.hxx>
#include <NXOpen/Annotations_PmiOrdinateOriginDimension.hxx>
#include <NXOpen/Annotations_PmiParallelDimension.hxx>
#include <NXOpen/Annotations_PmiPerpendicularDimension.hxx>
#include <NXOpen/Annotations_PmiRadiusDimension.hxx>
#include <NXOpen/Annotations_PmiVerticalDimension.hxx>
#include <NXOpen/Annotations_PmiVerticalOrdinateDimension.hxx>
#include <NXOpen/Annotations_PointTarget.hxx>
#include <NXOpen/Axis.hxx>
#include <NXOpen/BasePart.hxx>
#include <NXOpen/Body.hxx>
#include <NXOpen/Builder.hxx>
#include <NXOpen/CoordinateSystemCollection.hxx>
#include <NXOpen/CurveCollection.hxx>
#include <NXOpen/Direction.hxx>
#include <NXOpen/DisplayableObject.hxx>
#include <NXOpen/Drawings_DraftingDrawingSheet.hxx>
#include <NXOpen/Drawings_DraftingDrawingSheetCollection.hxx>
#include <NXOpen/Drawings_DrawingSheetCollection.hxx>
#include <NXOpen/Expression.hxx>
#include <NXOpen/ExpressionCollection.hxx>
#include <NXOpen/Features_AssociativeArc.hxx>
#include <NXOpen/Features_AssociativeArcBuilder.hxx>
#include <NXOpen/Features_BaseFeatureCollection.hxx>
#include <NXOpen/Features_Chamfer.hxx>
#include <NXOpen/Features_CylinderBuilder.hxx>
#include <NXOpen/Features_FeatureCollection.hxx>
#include <NXOpen/Features_LabelChamfer.hxx>
#include <NXOpen/GeometricUtilities_BooleanOperation.hxx>
#include <NXOpen/ICurve.hxx>
#include <NXOpen/ListingWindow.hxx>
#include <NXOpen/NXException.hxx>
#include <NXOpen/NXObject.hxx>
#include <NXOpen/NXObjectManager.hxx>
#include <NXOpen/Part.hxx>
#include <NXOpen/PartCollection.hxx>
#include <NXOpen/Point.hxx>
#include <NXOpen/PointCollection.hxx>
#include <NXOpen/SelectICurve.hxx>
#include <NXOpen/SelectObject.hxx>
#include <NXOpen/SelectPoint.hxx>
#include <NXOpen/Session.hxx>
#include <NXOpen/TaggedObject.hxx>
#include <NXOpen/UI.hxx>
#include <NXOpen/Unit.hxx>
#include <NXOpen/View.hxx>
#include <NXOpen/WCS.hxx>
#include <NXOpen/Xform.hxx>
#include <NXOpen/XformCollection.hxx>
#include <string>
#include <uf.h>
#include <uf_defs.h>
#include <uf_exit.h>
#include <uf_part.h>
#include <uf_ui.h>

enum class ANNOTATION_TYPE {
    NOT_ANNOTATION_TYPE,

    // base dimensions
    BASE_ANGULAR_DIMENSION,
    BASE_ARC_LENGTH_DIMENSION,
    BASE_CHAMFER_DIMENSION,
    BASE_CONCENTRIC_CIRCLE_DIMENSION,
    BASE_CYLINDRICAL_DIMENSION,
    BASE_DIAMETER_DIMENSION,
    BASE_FOLDED_RADIUS_DIMENSION,
    BASE_HOLE_DIMENSION,
    BASE_HORIZONTAL_DIMENSION,
    BASE_PARALLEL_DIMENSION,
    BASE_PERPENDICULAR_DIMENSION,
    BASE_RADIUS_DIMENSION,
    BASE_VERTICAL_DIMENSION,
    ORDINATE_DIMENSION,
    ORDINATE_ORIGIN_DIMENSION,

    PMI_CUSTOM_SYMBOL,
    PMI_NOTE,
    PMI_ATTRIBUTE,
    PMI_LINE_WELD,

    FCF,
    DATUM,
    POINT_TARGET,

	UNKNOWN,
};


ANNOTATION_TYPE GetConcreteAnnotationType(NXOpen::Annotations::Dimension* dimension);
ANNOTATION_TYPE GetConcreteAnnotationType(tag_t tag);

ANNOTATION_TYPE GetConcreteDimensionType(NXOpen::Annotations::Dimension* dimension);

NXOpen::Annotations::Dimension* GetDimensionFromTag(tag_t tag);


bool IsPmiInChildOfWorkPart();

void createPmiIdObject(NXOpen::Annotations::Dimension* dimension, NXOpen::Point3d origin);