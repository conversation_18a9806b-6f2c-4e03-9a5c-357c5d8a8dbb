# Qt6 Singleton Window - NX Plugin

## 🎯 Overview

This project implements a Qt6 singleton window that automatically opens when the NX plugin DLL is loaded. The window follows the singleton pattern, ensuring only one instance exists throughout the plugin's lifetime.

## ✅ Problem Solved

**Original Issue**: "Failed to load image - see log file for details" when loading `tw_demo.dll`

**Root Cause**: Missing Qt6 runtime libraries

**Solution**: Automatic Qt6 library deployment with every build

## 🚀 Features

- **Singleton Pattern**: Only one Qt window instance can exist
- **Thread-Safe**: Mutex-protected singleton access
- **Automatic Qt Deployment**: Qt libraries are automatically deployed after each build
- **Cross-Configuration**: Works with both Debug and Release builds
- **Message Logging**: Real-time message display with timestamps
- **User Interaction**: Hide/show window, clear messages
- **Proper Cleanup**: Automatic resource cleanup when DLL unloads

## 📁 Project Structure

```
tw_demo/
├── src/
│   ├── main.cpp                    # Main NX plugin entry point
│   ├── qt_singleton_window.cpp     # Qt singleton implementation
│   └── ...
├── include/
│   ├── qt_singleton_window.h       # Qt singleton header
│   └── ...
├── build/
│   ├── Debug/
│   │   ├── tw_demo.dll             # Debug DLL
│   │   ├── Qt6Cored.dll            # Qt6 Core (Debug)
│   │   ├── Qt6Guid.dll             # Qt6 GUI (Debug)
│   │   ├── Qt6Widgetsd.dll         # Qt6 Widgets (Debug)
│   │   └── platforms/              # Qt plugins
│   └── Release/
│       ├── tw_demo.dll             # Release DLL
│       ├── Qt6Core.dll             # Qt6 Core (Release)
│       ├── Qt6Gui.dll              # Qt6 GUI (Release)
│       ├── Qt6Widgets.dll          # Qt6 Widgets (Release)
│       └── platforms/              # Qt plugins
├── docs/
│   ├── qt_singleton_usage.md       # Usage guide
│   └── qt_deployment_troubleshooting.md # Troubleshooting
├── deploy_qt.bat                   # Manual deployment script
└── CMakeLists.txt                  # Build configuration
```

## 🔧 Build Instructions

### Automatic Build (Recommended)

```bash
# Debug build with automatic Qt deployment
cmake --build build --config Debug

# Release build with automatic Qt deployment  
cmake --build build --config Release
```

The Qt libraries will be automatically deployed to the same directory as `tw_demo.dll`.

### Manual Deployment (If Needed)

```bash
# Run the deployment script
deploy_qt.bat

# Or use windeployqt directly
D:\Qt\6.9.0\msvc2022_64\bin\windeployqt.exe --debug --dir "build\Debug" "build\Debug\tw_demo.dll"
```

## 📋 Deployed Files

After building, the following Qt6 files will be present alongside `tw_demo.dll`:

### Core Libraries
- `Qt6Core[d].dll` - Core Qt functionality
- `Qt6Gui[d].dll` - GUI functionality
- `Qt6Widgets[d].dll` - Widget functionality
- `Qt6Network[d].dll` - Network functionality
- `Qt6Svg[d].dll` - SVG support

### System Libraries
- `opengl32sw.dll` - Software OpenGL renderer
- `D3Dcompiler_47.dll` - DirectX shader compiler
- `dxcompiler.dll` & `dxil.dll` - DirectX compilers

### Plugin Directories
- `platforms/` - Platform plugins (Windows integration)
- `styles/` - UI style plugins
- `imageformats/` - Image format support
- `iconengines/` - Icon rendering
- `translations/` - Internationalization

## 🎮 Usage

### Loading the Plugin

1. Copy the entire contents of `build/Debug/` or `build/Release/` to your NX plugin directory
2. Load `tw_demo.dll` in NX
3. The Qt singleton window will automatically appear

### Interacting with the Window

```cpp
// Get the singleton instance
QtSingletonWindow* window = QtSingletonWindow::getInstance();

// Add messages
window->addMessage("Plugin operation started");
window->addMessage("Processing data...");

// Control visibility
window->showWindow();
window->hideWindow();

// Check status
if (window->isWindowVisible()) {
    // Window is currently visible
}
```

## 🔍 Verification

### Check Deployment Success

```bash
# Verify Qt DLLs are present
dir build\Debug\Qt6*.dll

# Verify plugins are present
dir build\Debug\platforms
```

### Test Loading

1. Load `tw_demo.dll` in NX
2. Qt singleton window should appear automatically
3. Check for messages in both NX Listing Window and Qt window

## 🛠️ Troubleshooting

### Common Issues

1. **"Failed to load image"** → Run `deploy_qt.bat` or rebuild
2. **"Qt platform plugin could not be initialized"** → Check `platforms/` directory
3. **Missing Visual C++ Runtime** → Install VC++ Redistributable 2022

### Debug Tools

- Use `dumpbin /dependents tw_demo.dll` to check dependencies
- Enable Qt debug output: `set QT_DEBUG_PLUGINS=1`
- Check Windows Event Viewer for system errors

See `docs/qt_deployment_troubleshooting.md` for detailed troubleshooting.

## 📚 Documentation

- [Qt Singleton Usage Guide](docs/qt_singleton_usage.md)
- [Qt Deployment Troubleshooting](docs/qt_deployment_troubleshooting.md)

## ✨ Key Benefits

1. **No Manual Deployment**: Qt libraries are automatically deployed
2. **Singleton Pattern**: Ensures only one window instance
3. **Thread Safety**: Safe to use from multiple threads
4. **Memory Management**: Automatic cleanup prevents memory leaks
5. **Cross-Platform Ready**: Can be extended for Linux/macOS
6. **Development Friendly**: Debug and Release configurations supported

## 🎯 Next Steps

Your `tw_demo.dll` should now load successfully in NX with the Qt singleton window appearing automatically. The window will persist throughout the plugin's lifetime and provide a convenient interface for logging and user interaction.

**Ready to use!** 🚀
